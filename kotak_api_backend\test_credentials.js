// Test script to verify Kotak API credentials
const axios = require('axios');
require('dotenv').config();

const {
  KOTAK_CONSUMER_KEY,
  KOTAK_CONSUMER_SECRET,
} = process.env;

async function testCredentials() {
  console.log('🔍 Testing Kotak API Credentials...\n');
  
  console.log('📋 Current Credentials:');
  console.log('Consumer Key:', KOTAK_CONSUMER_KEY);
  console.log('Consumer Secret:', KOTAK_CONSUMER_SECRET ? '***' + KOTAK_CONSUMER_SECRET.slice(-4) : 'NOT SET');
  console.log('');

  const credentials = `${KOTAK_CONSUMER_KEY}:${KOTAK_CONSUMER_SECRET}`;
  const encodedAuth = Buffer.from(credentials).toString('base64');
  
  console.log('🔐 Encoded Auth String:', encodedAuth.slice(0, 20) + '...');
  console.log('');

  try {
    console.log('📡 Making request to Kotak OAuth endpoint...');
    const response = await axios.post(
      'https://napi.kotaksecurities.com/oauth2/token',
      'grant_type=client_credentials',
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${encodedAuth}`,
        },
        timeout: 10000 // 10 second timeout
      }
    );
    
    console.log('✅ SUCCESS! Credentials are valid.');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.log('❌ FAILED! Credential test failed.');
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response Data:', JSON.stringify(error.response.data, null, 2));
      console.log('Response Headers:', error.response.headers);
    } else if (error.request) {
      console.log('Network Error - No response received');
      console.log('Request details:', error.request);
    } else {
      console.log('Error:', error.message);
    }
  }
}

testCredentials();
