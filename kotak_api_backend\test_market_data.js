// Test script to check market data API
const axios = require('axios');

async function testMarketDataAPI() {
  console.log('🧪 Testing Market Data API...\n');
  
  try {
    // First, check if we need to login
    console.log('1. Checking authentication status...');
    const statusResponse = await axios.get('http://localhost:3001/api/auth/status');
    console.log('Auth status:', statusResponse.data);
    
    if (!statusResponse.data.isLoggedIn) {
      console.log('❌ Not logged in. Please login first through the frontend.');
      return;
    }
    
    // Test market data endpoint
    console.log('\n2. Testing market data endpoint for NIFTY_50...');
    const marketResponse = await axios.get('http://localhost:3001/api/data/index-ltp?instrument=NIFTY_50');
    console.log('✅ Market data response:', marketResponse.data);
    
    console.log('\n3. Testing market data endpoint for BANK_NIFTY...');
    const bankNiftyResponse = await axios.get('http://localhost:3001/api/data/index-ltp?instrument=BANK_NIFTY');
    console.log('✅ Bank Nifty response:', bankNiftyResponse.data);
    
  } catch (error) {
    console.log('❌ Test failed:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
}

testMarketDataAPI();
