// Simple test script to verify our backend endpoints
const axios = require('axios');

async function testEndpoints() {
  console.log('🧪 Testing Backend Endpoints...\n');
  
  try {
    // Test 1: Status endpoint
    console.log('1. Testing GET /api/auth/status');
    const statusResponse = await axios.get('http://localhost:3001/api/auth/status');
    console.log('✅ Status Response:', statusResponse.data);
    
    // Test 2: Login endpoint with dummy TOTP
    console.log('\n2. Testing POST /api/auth/login with dummy TOTP');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      totp: '123456'
    });
    console.log('✅ Login Response:', loginResponse.data);
    
  } catch (error) {
    if (error.response) {
      console.log('📋 Response Status:', error.response.status);
      console.log('📋 Response Data:', error.response.data);
      if (error.response.status === 500) {
        console.log('✅ This is EXPECTED! The 500 error means the endpoint exists and is working.');
        console.log('   The error is because we used a dummy TOTP (123456).');
      }
    } else {
      console.log('❌ Network Error:', error.message);
    }
  }
}

testEndpoints();
