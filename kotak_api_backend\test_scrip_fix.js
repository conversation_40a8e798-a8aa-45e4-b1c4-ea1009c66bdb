// Test script to verify the fixed Scrip Master parsing
const kotakAuth = require('./src/services/kotakAuthService');
const scripMasterService = require('./src/services/scripMasterService');
require('dotenv').config();

async function testScripMasterFix() {
  console.log('🔧 Testing Fixed Scrip Master Service...\n');
  
  try {
    // Generate a client token first
    console.log('1. Generating client token...');
    const clientToken = await kotakAuth.generateClientToken();
    console.log('✅ Client token generated');
    
    // Initialize Scrip Master with the new logic
    console.log('\n2. Initializing Scrip Master with data-driven approach...');
    await scripMasterService.initializeFNOData(clientToken);
    console.log('✅ Scrip Master initialized');
    
    // Test the new getNearestExpiry function
    console.log('\n3. Testing getNearestExpiry function...');
    const niftyExpiry = scripMasterService.getNearestExpiry('NIFTY');
    const bankNiftyExpiry = scripMasterService.getNearestExpiry('BANKNIFTY');
    
    console.log(`✅ Nearest NIFTY expiry: ${niftyExpiry}`);
    console.log(`✅ Nearest BANKNIFTY expiry: ${bankNiftyExpiry}`);
    
    // Test option token lookup with the correct expiry
    if (niftyExpiry) {
      console.log('\n4. Testing option token lookup...');
      const ceToken = scripMasterService.getOptionToken('NIFTY', niftyExpiry, 25000, 'CE');
      const peToken = scripMasterService.getOptionToken('NIFTY', niftyExpiry, 25000, 'PE');
      
      console.log(`✅ NIFTY 25000 CE token: ${ceToken}`);
      console.log(`✅ NIFTY 25000 PE token: ${peToken}`);
    }
    
    console.log('\n🎉 Scrip Master fix test completed successfully!');
    
  } catch (error) {
    console.log('❌ Scrip Master fix test failed:');
    console.log('Error:', error.message);
  }
}

testScripMasterFix();
