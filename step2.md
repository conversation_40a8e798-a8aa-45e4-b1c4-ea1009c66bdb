Of course. Congratulations on successfully setting up and verifying the backend in Step 1. The 500 Internal Server Error with the "invalid_client" message is the perfect result for a test with dummy credentials, as it proves your server's logic is executing correctly.

Now, let's build the user interface that will interact with our secure backend.

Project: Nifty/Bank Nifty Options Trading Panel
Step 2 of 5: The Frontend Login Interface

Objective:
In this step, we will create a clean, functional frontend using React, a modern JavaScript library perfect for building dynamic user interfaces. This frontend will provide a simple login form where the user can enter their Time-based One-Time Password (TOTP). It will communicate with the backend server we built in Step 1 to handle the entire login process, display the current login status, and show any errors to the user.

Technology Choice:

Frontend Framework: React with Vite

Why? Vite provides an extremely fast development environment with instant server start-up and lightning-fast updates (Hot Module Replacement). React is the industry standard for building component-based UIs that can efficiently manage and display changing data, like login status or live market prices.

Part 2.1: Frontend Project Setup

We will create a separate project for our frontend. It is standard practice to keep the backend and frontend in different folders.

Navigate Out of the Backend Directory: Open a new terminal window or navigate out of your kotak_api_backend folder.

Generated bash
cd ..


Create the React App with Vite: Run the following command. It will prompt you for a project name and framework choice.

Generated bash
npm create vite@latest
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

When prompted for Project name, type kotak_trading_frontend and press Enter.

When asked to Select a framework, use the arrow keys to choose React and press Enter.

When asked to Select a variant, choose JavaScript and press Enter.

Navigate and Install Dependencies:

Generated bash
cd kotak_trading_frontend
npm install
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END
Part 2.2: Cleaning the Default Template

Vite gives us a default template. Let's clean it up for our specific needs.

Delete Unused Files: In the kotak_trading_frontend/src/ directory, delete the following files:

App.css

assets folder (and its contents)

Simplify main.jsx: Open src/main.jsx and make it look like this:

Generated javascript
// src/main.jsx
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'
import './index.css' // We will keep this for styling

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
Part 2.3: Building the Core Login Component (App.jsx)

This will be our main application component for now. It will manage the entire login UI and its state.

Open src/App.jsx and replace its entire content with the following code:

Generated javascript
// src/App.jsx

import React, { useState, useEffect } from 'react';

// The URL of the backend server we built in Step 1
const API_BASE_URL = 'http://localhost:3001';

function App() {
  // --- State Management ---
  // Holds the value of the TOTP input field
  const [totp, setTotp] = useState('');
  // Tracks if the user is currently authenticated
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  // Shows a loading spinner/message during API calls
  const [isLoading, setIsLoading] = useState(true);
  // Stores any error messages from the backend
  const [error, setError] = useState('');

  // --- Side Effects ---
  // This `useEffect` runs once when the component first loads
  useEffect(() => {
    // Check the server for an existing session
    const checkAuthStatus = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/auth/status`);
        const data = await response.json();
        setIsLoggedIn(data.isLoggedIn);
      } catch (err) {
        setError('Cannot connect to the backend server. Is it running?');
      } finally {
        setIsLoading(false);
      }
    };
    checkAuthStatus();
  }, []); // The empty array [] means this effect runs only once

  // --- Event Handlers ---
  const handleLogin = async (event) => {
    event.preventDefault(); // Prevent the form from reloading the page
    setIsLoading(true);
    setError(''); // Clear previous errors

    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ totp }),
      });

      const data = await response.json();

      if (response.ok) { // Status code 200-299
        setIsLoggedIn(true);
      } else {
        setError(data.message || 'An unknown error occurred.');
      }
    } catch (err) {
      setError('Network error: Could not reach the server.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    await fetch(`${API_BASE_URL}/api/auth/logout`, { method: 'POST' });
    setIsLoggedIn(false);
    setTotp('');
    setError('');
    setIsLoading(false);
  };

  // --- UI Rendering ---
  if (isLoading) {
    return <div className="container"><h1>Loading...</h1></div>;
  }

  return (
    <div className="container">
      <header>
        <h1>Kotak Neo Trading Panel</h1>
        <div className={`status-indicator ${isLoggedIn ? 'logged-in' : 'logged-out'}`}>
          {isLoggedIn ? 'Status: Connected' : 'Status: Disconnected'}
        </div>
      </header>
      
      <main>
        {isLoggedIn ? (
          <div className="session-view">
            <h2>Welcome!</h2>
            <p className="success-message">You are successfully authenticated.</p>
            <p>We are now ready to fetch market data.</p>
            <button onClick={handleLogout} disabled={isLoading}>
              Logout
            </button>
          </div>
        ) : (
          <form onSubmit={handleLogin} className="login-form">
            <h2>Login Required</h2>
            <p>Please enter your TOTP from your authenticator app.</p>
            <div className="form-group">
              <label htmlFor="totp">One-Time Password (TOTP)</label>
              <input
                id="totp"
                type="text"
                value={totp}
                onChange={(e) => setTotp(e.target.value)}
                placeholder="e.g., 123456"
                required
              />
            </div>
            <button type="submit" disabled={isLoading}>
              {isLoading ? 'Logging in...' : 'Login'}
            </button>
            {error && <p className="error-message">{error}</p>}
          </form>
        )}
      </main>
    </div>
  );
}

export default App;
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
Part 2.4: Basic Styling (index.css)

To make the application look clean and usable, open src/index.css and replace its entire content with these styles.

Generated css
/* src/index.css */

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  background-color: #242424;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  width: 100%;
}

header {
  border-bottom: 1px solid #444;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

h1 {
  font-size: 2.5em;
  line-height: 1.1;
  margin-bottom: 0.5rem;
}

.status-indicator {
  padding: 0.3rem 0.6rem;
  border-radius: 8px;
  display: inline-block;
  font-weight: bold;
}

.status-indicator.logged-in {
  background-color: #28a745;
  color: white;
}

.status-indicator.logged-out {
  background-color: #dc3545;
  color: white;
}

.login-form, .session-view {
  background-color: #2f2f2f;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #444;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

input {
  width: 100%;
  padding: 0.8rem;
  font-size: 1em;
  border-radius: 4px;
  border: 1px solid #555;
  background-color: #333;
  color: white;
  box-sizing: border-box;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.error-message {
  color: #ff6b6b;
  margin-top: 1rem;
  font-weight: bold;
}

.success-message {
  color: #28a745;
  font-weight: bold;
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Css
IGNORE_WHEN_COPYING_END
Part 2.5: Running and Verifying Your Application

Now we will run both the backend and the frontend simultaneously.

Terminal 1 (Backend):

Navigate to your kotak_api_backend folder.

Run the server: npm start

Keep this terminal running.

Terminal 2 (Frontend):

Navigate to your kotak_trading_frontend folder.

Run the development server: npm run dev

This will output a URL, usually http://localhost:5173.

Open Your Web Browser: Navigate to http://localhost:5173.

Testing Scenarios to Verify:

Initial Load: The page should briefly say "Loading..." and then show the login form with the status "Status: Disconnected".

Failed Login: Enter an incorrect/expired TOTP and click "Login". You should see the button text change to "Logging in...", then an error message should appear below, like "View Token Generation Failed. Check your TOTP."

Successful Login: Get a fresh, valid TOTP from your authenticator app, enter it, and click "Login". The form should disappear and be replaced with the "Welcome!" message and the status should change to "Status: Connected".

Logout: Click the "Logout" button. You should be returned to the login form.

Session Persistence: After a successful login, refresh the browser page. It should briefly show "Loading..." and then immediately go to the "Welcome!" screen without requiring you to log in again. This confirms the useEffect status check is working correctly.

You have now completed Step 2. You have a working, interactive user interface that can securely authenticate with your backend. You are now ready to ask for Step 3.