// Test script to debug Scrip Master CSV structure
const kotakAuth = require('./src/services/kotakAuthService');
const scripMasterService = require('./src/services/scripMasterService');
require('dotenv').config();

async function testScripMaster() {
  console.log('🧪 Testing Scrip Master CSV Structure...\n');
  
  try {
    // Generate a client token first
    console.log('1. Generating client token...');
    const clientToken = await kotakAuth.generateClientToken();
    console.log('✅ Client token generated');
    
    // Initialize Scrip Master to see the CSV structure
    console.log('\n2. Initializing Scrip Master...');
    await scripMasterService.initialize(clientToken);
    console.log('✅ Scrip Master initialized');
    
  } catch (error) {
    console.log('❌ Test failed:');
    console.log('Error:', error.message);
    if (error.response) {
      console.log('Response data:', error.response.data);
    }
  }
}

testScripMaster();
