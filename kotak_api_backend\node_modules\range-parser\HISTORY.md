1.2.1 / 2019-05-10
==================

  * Improve error when `str` is not a string

1.2.0 / 2016-06-01
==================

  * Add `combine` option to combine overlapping ranges

1.1.0 / 2016-05-13
==================

  * Fix incorrectly returning -1 when there is at least one valid range
  * perf: remove internal function

1.0.3 / 2015-10-29
==================

  * perf: enable strict mode

1.0.2 / 2014-09-08
==================

  * Support Node.js 0.6

1.0.1 / 2014-09-07
==================

  * Move repository to jshttp

1.0.0 / 2013-12-11
==================

  * Add repository to package.json
  * Add MIT license

0.0.4 / 2012-06-17
==================

  * Change ret -1 for unsatisfiable and -2 when invalid

0.0.3 / 2012-06-17
==================

  * Fix last-byte-pos default to len - 1

0.0.2 / 2012-06-14
==================

  * Add `.type`

0.0.1 / 2012-06-11
==================

  * Initial release
