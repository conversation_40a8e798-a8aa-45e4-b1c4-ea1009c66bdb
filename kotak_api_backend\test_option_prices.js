// Test script to check what option prices we're actually getting
const kotakAuth = require('./src/services/kotakAuthService');
const scripMasterService = require('./src/services/scripMasterService');
const axios = require('axios');
require('dotenv').config();

async function testOptionPrices() {
  console.log('🔍 Testing Option Price Fetching...\n');
  
  try {
    // 1. Authenticate
    console.log('1. Authenticating...');
    const clientToken = await kotakAuth.generateClientToken();
    const { viewToken, sid } = await kotakAuth.generateViewToken(clientToken, '123456'); // You'll need to provide real TOTP
    await kotakAuth.generateTradingToken(clientToken, viewToken, sid);
    
    // 2. Initialize Scrip Master
    console.log('2. Initializing Scrip Master...');
    await scripMasterService.initializeFNOData(clientToken);
    
    // 3. Test option token lookup
    console.log('3. Testing option token lookup...');
    const testExpiry = '20250116'; // Example expiry
    const testStrike = 25000;
    
    const ceToken = scripMasterService.getOptionToken('NIFTY', testExpiry, testStrike, 'CE');
    const peToken = scripMasterService.getOptionToken('NIFTY', testExpiry, testStrike, 'PE');
    
    console.log(`NIFTY ${testStrike} CE token: ${ceToken}`);
    console.log(`NIFTY ${testStrike} PE token: ${peToken}`);
    
    if (ceToken && peToken) {
      // 4. Test actual price fetching
      console.log('\n4. Testing price fetching from Kotak API...');
      const neoSymbols = [`nse_fo|${ceToken}`, `nse_fo|${peToken}`];
      const symbolsString = neoSymbols.join(',');
      const quotesApiUrl = `https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/${encodeURIComponent(symbolsString)}/ltp`;
      
      console.log(`API URL: ${quotesApiUrl}`);
      
      const response = await axios.get(quotesApiUrl, {
        headers: { 'Authorization': `Bearer ${clientToken}` }
      });
      
      console.log('\n5. Raw API Response:');
      console.log(JSON.stringify(response.data, null, 2));
      
      // 6. Test our parsing logic
      console.log('\n6. Testing our parsing logic...');
      const quotesMap = new Map();
      if (response.data && Array.isArray(response.data)) {
        response.data.forEach(quote => {
          console.log(`Quote object:`, quote);
          if (quote.exchange_token && quote.ltp) {
            quotesMap.set(quote.exchange_token, quote.ltp);
            console.log(`Mapped: ${quote.exchange_token} -> ${quote.ltp}`);
          }
        });
      }
      
      console.log('\n7. Final price lookup:');
      console.log(`CE Price: ${quotesMap.has(ceToken) ? quotesMap.get(ceToken) : 'NOT FOUND'}`);
      console.log(`PE Price: ${quotesMap.has(peToken) ? quotesMap.get(peToken) : 'NOT FOUND'}`);
    }
    
  } catch (error) {
    console.log('❌ Test failed:');
    console.log('Error:', error.message);
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
  }
}

// Note: You'll need to provide a real TOTP when running this
console.log('⚠️  Please update the TOTP in the code before running this test');
// testOptionPrices();
