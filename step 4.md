Excellent. You have built a robust and professional-grade foundation. The decision to switch to the Yahoo Finance API for the spot index prices while keeping the entire Kotak authentication flow intact is a superb architectural choice. It solves the immediate problem reliably and sets us up perfectly for the next phase.

You are absolutely ready for Step 4. Let's build the Option Chain.

Project: Nifty/Bank Nifty Options Trading Panel
Step 4 of 5: Building and Displaying the Option Chain

Objective:
In this crucial step, we will leverage our authenticated Kotak session to build a full option chain. The process will be:

Use the live index price (from Yahoo Finance) to determine the At-the-Money (ATM) strike.

Calculate a range of strike prices around the ATM.

Download and process the Kotak nse_fo.csv Scrip Master file to find the official numeric tokens for each required option contract (Calls and Puts).

Use the Kotak Quotes API to fetch the prices for all these options in a single, efficient, bulk request.

Display the complete option chain data in a professional table on the frontend.

Part 4.1: Backend - Upgrading the scripMasterService.js

It's time to build out the initializeFNOData function we created earlier. This service will now download, parse, and cache the entire F&O (Futures & Options) scrip master file.

Modify the file: src/services/scripMasterService.js

Generated javascript
// src/services/scripMasterService.js

const axios = require('axios');
const { parse } = require('papaparse');

// This cache will hold all Futures & Options instruments.
let fnoInstrumentCache = new Map();
let isInitialized = false;

const SCRIP_MASTER_FILE_PATHS_URL = 'https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths';

/**
 * Initializes the service by downloading, parsing, and caching the NSE F&O scrip master.
 * @param {string} accessToken - A valid client access token.
 */
const initializeFNOData = async (accessToken) => {
  console.log('[ScripMaster] Initializing F&O data service...');
  try {
    const filePathsResponse = await axios.get(SCRIP_MASTER_FILE_PATHS_URL, {
      headers: { 'Authorization': `Bearer ${accessToken}` },
    });
    
    const nseFoUrl = filePathsResponse.data.data.filesPaths.find(p => p.includes('nse_fo.csv'));
    if (!nseFoUrl) throw new Error('NSE FO scrip master URL not found.');

    console.log(`[ScripMaster] Downloading F&O file from: ${nseFoUrl}`);
    const csvResponse = await axios.get(nseFoUrl);
    if (!csvResponse.data) throw new Error('Downloaded F&O Scrip Master file is empty.');

    console.log('[ScripMaster] Parsing F&O CSV data...');
    const parsedData = parse(csvResponse.data, { header: true, skipEmptyLines: true });
    
    fnoInstrumentCache.clear();
    parsedData.data.forEach(instrument => {
      // Create a unique, predictable key for each option contract for easy lookup.
      // Format: SYMBOL-YYYYMMDD-STRIKE-OPTIONTYPE (e.g., NIFTY-20240725-25000-CE)
      const key = `${instrument.pSymbol.trim()}-${instrument.lExpiryDate.split(' ')[0]}-${parseFloat(instrument.dStrikePrice) / 100}-${instrument.cOptType.trim()}`;
      instrumentCache.set(key.toUpperCase(), instrument.pToken.trim());
    });

    isInitialized = true;
    console.log(`[ScripMaster] SUCCESS. Cached ${fnoInstrumentCache.size} F&O instruments.`);
  } catch (error) {
    console.error('[ScripMaster] FATAL ERROR during initialization:', error.message);
    isInitialized = false;
    throw error;
  }
};

/**
 * Gets the numeric token for a specific option contract from the cache.
 * @param {string} symbol - e.g., 'NIFTY'
 * @param {string} expiryDate - e.g., '20240725' (YYYYMMDD)
 * @param {number} strike - e.g., 25000
 * @param {string} optionType - 'CE' or 'PE'
 * @returns {string|null} The numeric token or null if not found.
 */
const getOptionToken = (symbol, expiryDate, strike, optionType) => {
  if (!isInitialized) return null;
  const key = `${symbol}-${expiryDate}-${strike}-${optionType}`.toUpperCase();
  return instrumentCache.get(key) || null;
};


// We no longer need getIndexToken here, so we remove it.
module.exports = {
  initializeFNOData,
  getOptionToken,
};

Part 4.2: Backend - The New Option Chain API Endpoint

This is the core new feature. We'll add a powerful endpoint to server.js that does all the heavy lifting.

Modify the file: src/server.js

Generated javascript
// src/server.js
// ... (keep all existing imports and setup)
const scripMasterService = require('./services/scripMasterService');

// ... (keep all existing API routes: /login, /status, /logout, /index-ltp)

// --- ADD THE NEW OPTION CHAIN ROUTE BELOW ---

/**
 * GET /api/data/option-chain
 * Builds and returns the option chain data for a given instrument.
 * Query Params:
 *  - instrument: 'NIFTY_50' or 'BANK_NIFTY'
 *  - indexPrice: The current LTP of the index (e.g., 25149.85)
 */
app.get('/api/data/option-chain', async (req, res) => {
  console.log(`\n--- Received Option Chain Request ---`);
  
  const { accessToken } = tokenManager.getTokens();
  if (!accessToken) {
    return res.status(401).json({ success: false, message: 'Unauthorized.' });
  }

  const { instrument, indexPrice } = req.query;
  if (!instrument || !indexPrice) {
    return res.status(400).json({ success: false, message: 'Instrument and indexPrice are required.' });
  }
  
  try {
    // 1. Determine parameters based on the instrument
    const isNifty = instrument === 'NIFTY_50';
    const underlyingSymbol = isNifty ? 'NIFTY' : 'BANKNIFTY';
    const strikeDifference = isNifty ? 50 : 100;
    const atmStrike = Math.round(parseFloat(indexPrice) / strikeDifference) * strikeDifference;

    // 2. Find the nearest weekly expiry date (a Thursday)
    const today = new Date();
    const dayOfWeek = today.getDay(); // Sunday = 0, Thursday = 4
    let daysUntilExpiry = 4 - dayOfWeek;
    if (daysUntilExpiry < 0) daysUntilExpiry += 7; // If it's Fri/Sat, get next week's Thursday
    const expiryDate = new Date();
    expiryDate.setDate(today.getDate() + daysUntilExpiry);
    const expiryDateString = expiryDate.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD format

    // 3. Generate strike prices and find their tokens
    const strikePrices = [];
    const neoSymbolsToFetch = [];
    for (let i = -5; i <= 5; i++) {
      const strike = atmStrike + (i * strikeDifference);
      strikePrices.push(strike);
      
      const ceToken = scripMasterService.getOptionToken(underlyingSymbol, expiryDateString, strike, 'CE');
      const peToken = scripMasterService.getOptionToken(underlyingSymbol, expiryDateString, strike, 'PE');
      
      if (ceToken) neoSymbolsToFetch.push(`nse_fo|${ceToken}`);
      if (peToken) neoSymbolsToFetch.push(`nse_fo|${peToken}`);
    }

    if (neoSymbolsToFetch.length === 0) {
      throw new Error(`Could not find any option tokens for ${underlyingSymbol} with expiry ${expiryDateString}. Is the Scrip Master initialized?`);
    }

    // 4. Make a single, bulk API call to Kotak for all option prices
    const symbolsString = neoSymbolsToFetch.join(',');
    const quotesApiUrl = `https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/${encodeURIComponent(symbolsString)}/ltp`;
    
    console.log(`[Opt. Chain] Fetching prices for ${neoSymbolsToFetch.length} tokens...`);
    const quotesResponse = await axios.get(quotesApiUrl, {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });

    // 5. Map the results back to our strike prices
    const quotesMap = new Map(quotesResponse.data.map(q => [q.exchange_token, q.ltp]));
    const optionChain = strikePrices.map(strike => {
      const ceToken = scripMasterService.getOptionToken(underlyingSymbol, expiryDateString, strike, 'CE');
      const peToken = scripMasterService.getOptionToken(underlyingSymbol, expiryDateString, strike, 'PE');
      return {
        strike,
        ce_ltp: quotesMap.has(ceToken) ? parseFloat(quotesMap.get(ceToken)) : 'N/A',
        pe_ltp: quotesMap.has(peToken) ? parseFloat(quotesMap.get(peToken)) : 'N/A',
      };
    });
    
    res.json({ success: true, optionChain });

  } catch (error) {
    const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error('[Opt. Chain] FAILED:', errorMsg);
    res.status(500).json({ success: false, message: `Failed to build option chain: ${errorMsg}` });
  }
});

// ... (keep the app.listen line at the end)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

Action: Restart your backend server (CTRL+C, then npm start).

Part 4.3: Frontend - Displaying the Option Chain

We will now modify our TradingView component to fetch and render the option chain table.

Modify the file: src/App.jsx

Generated javascript
// src/App.jsx
// ... (keep all existing React imports)

// --- TradingView Component (Heavily Modified) ---
function TradingView({ onLogout }) {
  const [selectedInstrument, setSelectedInstrument] = useState('NIFTY_50');
  const [indexPrice, setIndexPrice] = useState(null);
  const [optionChain, setOptionChain] = useState([]); // <-- New state for option chain data
  const [dataIsLoading, setDataIsLoading] = useState(true);
  const [dataError, setDataError] = useState('');

  const intervalRef = useRef(null);

  useEffect(() => {
    // This function will now fetch both index price and the option chain
    const fetchData = async () => {
      setDataIsLoading(true);
      setDataError('');
      try {
        // --- Step 1: Fetch the live index price (from Yahoo) ---
        const indexResponse = await fetch(`${API_BASE_URL}/api/data/index-ltp?instrument=${selectedInstrument}`);
        const indexData = await indexResponse.json();
        
        if (!indexResponse.ok) throw new Error(indexData.message || 'Failed to fetch index price');
        
        const currentPrice = indexData.ltp;
        setIndexPrice(currentPrice);

        // --- Step 2: Fetch the option chain using the live index price (from Kotak) ---
        const ocResponse = await fetch(`${API_BASE_URL}/api/data/option-chain?instrument=${selectedInstrument}&indexPrice=${currentPrice}`);
        const ocData = await ocResponse.json();

        if (!ocResponse.ok) throw new Error(ocData.message || 'Failed to fetch option chain');

        setOptionChain(ocData.optionChain);

      } catch (err) {
        setDataError(err.message);
        setOptionChain([]); // Clear old data on error
      } finally {
        setDataIsLoading(false);
      }
    };

    fetchData();
    intervalRef.current = setInterval(fetchData, 5000); // Refreshes everything every 5s

    return () => clearInterval(intervalRef.current);
  }, [selectedInstrument]);

  return (
    <div className="session-view">
      {/* ... (keep the instrument selector dropdown) ... */}
      <div className="price-display-wrapper">
        {/* ... (keep the price display) ... */}
      </div>

      {/* --- New Option Chain Table --- */}
      <div className="option-chain-container">
        {dataIsLoading && !optionChain.length ? (
          <p>Loading Option Chain...</p>
        ) : dataError ? (
          <p className="error-message">{dataError}</p>
        ) : (
          <table>
            <thead>
              <tr>
                <th>CALL LTP</th>
                <th>STRIKE</th>
                <th>PUT LTP</th>
              </tr>
            </thead>
            <tbody>
              {optionChain.map(({ strike, ce_ltp, pe_ltp }) => (
                <tr key={strike} className={Math.abs(strike - indexPrice) < 50 ? 'atm-strike' : ''}>
                  <td className="ce-price">{ce_ltp}</td>
                  <td className="strike-price">{strike}</td>
                  <td className="pe-price">{pe_ltp}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      <button onClick={onLogout}>Logout</button>
    </div>
  );
}

// ... (keep the rest of the App component and the login form)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

Modify the file: src/index.css

Add these styles to the bottom of src/index.css to format our new table.

Generated css
/* src/index.css */
/* ... (keep all existing CSS) ... */

/* --- ADD THESE NEW STYLES --- */
.option-chain-container {
  margin-top: 2rem;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 0.75rem;
  border: 1px solid #444;
}

th {
  background-color: #3a3a3a;
}

.strike-price {
  font-weight: bold;
  background-color: #3f4e62;
}

.ce-price {
  color: #a6ffb8; /* Light green for calls */
}

.pe-price {
  color: #ff8a8a; /* Light red for puts */
}

.atm-strike {
  background-color: #4a4a2a;
  font-weight: bold;
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Css
IGNORE_WHEN_COPYING_END
Part 4.4: How to Test Your Complete Option Chain

Restart Both Servers: Stop and restart both your backend and frontend servers to ensure all new code is loaded.

Hard Refresh the Browser: Go to http://localhost:5173 and press Ctrl+Shift+R.

Log In: Perform a fresh login with a valid TOTP.

Observe the Result:

After login, the "Market Data" view will appear.

It will first display the live index price.

Immediately after, the "Loading Option Chain..." message should appear, followed by a fully populated table showing 11 strike prices (5 above, 5 below, and the ATM strike).

The At-the-Money (ATM) strike row should be highlighted.

Switching the dropdown to Bank Nifty should repeat the process and show the correct option chain for Bank Nifty.

Check your backend terminal to see the logs from the [Opt. Chain] service, confirming it is finding and fetching the option tokens.

You have now successfully built the core feature of your application. You have a system that can authenticate, fetch a live index price, and use that price to dynamically build and display a real-time option chain using the official Kotak API. You are now ready for the final step.