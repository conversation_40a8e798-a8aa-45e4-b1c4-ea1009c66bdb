// Test the server directly with detailed logging
const axios = require('axios');

async function testServerDirect() {
  console.log('🔍 Testing server endpoint directly...\n');
  
  try {
    console.log('Making POST request to http://localhost:3001/api/auth/login');
    console.log('Request body: { "totp": "123456" }');
    
    const response = await axios.post('http://localhost:3001/api/auth/login', {
      totp: '123456'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log('✅ Response received:');
    console.log('Status:', response.status);
    console.log('Data:', response.data);
    
  } catch (error) {
    console.log('❌ Request failed:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
      console.log('Headers:', error.response.headers);
    } else if (error.request) {
      console.log('No response received');
      console.log('Request:', error.request);
    } else {
      console.log('Error:', error.message);
    }
  }
}

// Wait a moment for server to start, then test
setTimeout(testServerDirect, 2000);
