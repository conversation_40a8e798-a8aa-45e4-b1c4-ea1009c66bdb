# Kotak Trading Panel - Backend Server

This server provides a secure and reliable API interface for authenticating with the Kotak Neo Trade API.

## Installation & Setup

1.  **Install Node.js:** Make sure you have Node.js (v16 or higher) installed.
2.  **Install Dependencies:**
    ```bash
    npm install
    ```
3.  **Configure Credentials:**
    -   Copy `.env.example` to a new file named `.env`.
    -   Open `.env` and fill in your actual Kotak API and Demat account details.

## Running the Server

```bash
npm start
```

The server will start on port 3001 by default.

## API Testing

Use a tool like Postman or Insomnia to test the endpoints.

### Login

**URL:** http://localhost:3001/api/auth/login

**Method:** POST

**Body:** raw (JSON)

```json
{
  "totp": "YOUR_CURRENT_TOTP"
}
```

**Success Response (200 OK):** `{ "success": true, "message": "Login successful!" }`

### Check Status

**URL:** http://localhost:3001/api/auth/status

**Method:** GET

**Response:** `{ "isLoggedIn": true }` or `{ "isLoggedIn": false }`

### Logout

**URL:** http://localhost:3001/api/auth/logout

**Method:** POST

**Response:** `{ "success": true, "message": "Logged out successfully." }`

## Project Structure

```
kotak_api_backend/
├── src/
│   ├── services/
│   │   ├── kotakAuthService.js  # All logic for the 3-step auth flow
│   │   └── tokenManager.js      # A simple in-memory store for tokens
│   └── server.js                # The main Express server and API routes
├── .env                         # Your secret keys (DO NOT COMMIT)
├── .env.example                 # Template for environment variables
├── package.json
└── README.md
```

## Security Notes

- The `.env` file contains your secret credentials and should NEVER be committed to version control
- All authentication tokens are stored in memory and will be cleared when the server restarts
- This is a development setup - for production, consider using a proper database for token storage
