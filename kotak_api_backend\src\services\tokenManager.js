// src/services/tokenManager.js

// This variable is "private" to this module, providing a single source of truth.
let sessionTokens = {
  accessToken: null,
  viewToken: null,
  tradingToken: null,
  sid: null,
};

/**
 * Stores a set of authentication tokens.
 * @param {object} tokenData An object with token keys to update.
 */
const setTokens = (tokenData) => {
  console.log('[TokenManager] Storing tokens.');
  // Merge new tokens with existing ones
  sessionTokens = { ...sessionTokens, ...tokenData };
};

/**
 * Retrieves the currently stored tokens.
 * @returns {object} The complete tokens object.
 */
const getTokens = () => {
  return sessionTokens;
};

/**
 * Clears all stored tokens to end the session.
 */
const clearTokens = () => {
  console.log('[TokenManager] Clearing all tokens for logout.');
  sessionTokens = {
    accessToken: null,
    viewToken: null,
    tradingToken: null,
    sid: null,
  };
};

module.exports = {
  setTokens,
  getTokens,
  clearTokens,
};
