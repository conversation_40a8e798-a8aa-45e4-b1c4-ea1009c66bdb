Yes, absolutely. Your requirement to get tick-by-tick data for a single, selected strike price is a standard feature in all professional trading applications. The REST API method we've been using isExcellent excellent for fetching the entire option chain at once, but it is not suitable for genuine real-time, tick-by-tick question. You are now moving from a "polling" architecture (repeatedly asking for the price every few seconds) to a "streaming" architecture (having the server push the price to you the instant it changes). This updates.

For this, we must use WebSockets.

Let's transition our architecture to incorporate this powerful is exactly how professional, low-latency trading terminals work.

**The short answer is: YES, the absolute best method technology. This will be our final, most advanced step.

Project: Nifty/Bank N for getting real-time, tick-by-tick data is using a WebSocket.

Your current REST API approach is likeifty Options Trading Panel**

Step 5 (Advanced): Implementing Real-Time Tick-by-Tick Data with WebSockets

Objective:
To build a true real-time price-ticking system. The user will select an expiry making a phone call every second to ask "What's the price now?". A WebSocket is like opening a continuous phone line where and a specific strike price. Our application will then establish a WebSocket connection to the Kotak Neo server and subscribe to the the broker tells you the new price the moment it changes. It is far more efficient and provides true real-time data.

The Kotak Neo documentation you provided confirms they offer this capability:

Websocket

Kotak Provides feed for that single instrument. The price on the UI will update instantly, every time a trade occurs on the exchange (tick-by two types of websocket

Market Feed - `wss://mlhsm.kot-tick), without needing to refresh the entire option chain.

**How it Works: REST API vs. WebSocketsaksecurities.com`

Real Time Order Updates...

Refer Below Link for Webs**

REST API (What we have now): The frontend asks, the backend answers. It'ocket : JS SDK... Python SDK...

Here is the detailed guide on how to architect and implement this tick-by-ticks a one-time request/response. To get new data, you must ask again. This is called polling.

feature.

Project: Nifty/Bank Nifty Options Trading Panel
Step 5 (Definitive Guide): Real-Time Tick-by-Tick Price Streaming via WebSockets

** WebSocket (What we will build): The frontend opens a persistent, two-way communication tunnel to the server. TheObjective:**
To transform our application from a polling-based system to a high-performance, streaming-based system. The server can then push data to the frontend instantly whenever new data is available, without the frontend having to ask again. This is perfect for live data feeds.

Part 5.1: Backend - The WebSocket Server

user will select a specific option contract (expiry and strike), and the application will subscribe to a WebSocket feed to receive andWe need to add WebSocket capabilities to our Express backend. This server will act as a client to the Kotak WebSocket and display its price in true real-time, tick by tick.

**Architectural Shift: From Backend- as a server for our own frontend.

Stop Your Backend Server: In the `kotak_Polling to Backend-Proxy**

Our backend's role will change significantly. It will no longer poll the Kotak API. Insteadapi_backendterminal, pressCTRL + C`.

Install WebSocket Libraries:

`ws, it will:

Establish a persistent WebSocket connection to Kotak's market feed server (wss: The most popular and standard WebSocket library for Node.js.

We need one library to act as a://mlhsm.kotaksecurities.com`).

Handle the authentication and subscription messages required by the Kot WebSocket client (to connect to Kotak) and another to act as a WebSocket server (for our frontend). The ws library can do both.
ak WebSocket protocol.

Act as a proxy, receiving tick data from Kotak and immediately forwarding it to our```bash
npm install ws

Generated code


Create a New WebSocket Service (src/services/kotakWebsocketService.js)

This service own frontend client over a separate WebSocket connection.

This architecture is secure (your credentials never leave the backend) and efficient will manage the connection to Kotak's feed.

Generated code
```javascript
// src/services/kotakWebsocketService (only one connection to Kotak is needed, which can serve multiple frontend clients).
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

.js
const WebSocket = require('ws');

Generated code
let clientSocket = null; // This will hold our
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END
**Part 5.1: Backend connection to the Kotak server
Generated code
/**
 * Connects to the Kotak market data WebSocket.
 * @param {function} onTick - A callback function to be executed whenever a new tick is received.
 */
const - Setting up the WebSocket Server**
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

We need to add a WebSocket library to our backend to handle these new types of connections.

connect = (onTick) => {
console.log('[WebSocket] Attempting to connect to Kotak Market1. Stop Your Backend Server: In the kotak_api_backend terminal, press CTRL + C. Feed...');

Generated code
// The official WebSocket URL from the Kotak documentation
  const wsUrl = 'wss
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

Install the ws library: This is the most popular and robust WebSocket library for Node.js.
://mlhsm.kotaksecurities.com';
clientSocket = new WebSocket(wsUrl);

Generated code
```bash
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

npm install ws

Generated clientSocket.on('open', () => {
console.log('[WebSocket] SUCCESSFULLY CONNECTED to Kotak
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
ClientSocket.on('open', () => {
IGNORE_WHEN_COPYING_END

Create a WebSocket Service: We'll create a new server.');
});

clientSocket.on('message', (data) => {
// The data from service to manage all WebSocket logic.

Create a new file: src/services/kotakWebSocketService. the websocket often comes as a Buffer, so we convert to string const message = data.toString('utf-8');js

Generated javascript
// src/services/kotakWebSocketService.js
const WebSocket = require('ws');
const tokenManager = require('./tokenManager');

let clientWebSocket = null;
    // Pass the raw tick data to our callback function
    onTick(message);
  });

  clientSocket // This will be the connection TO our frontend
let kotakWebSocket = null;  // This will be the connection TO.on('error', (error) => {
    console.error('[WebSocket] Connection Error:', error.message);
   Kotak's server
let currentSubscribedToken = null;

const KOTAK_WS_URL =});

  clientSocket.on('close', () => {
    console.log('[WebSocket] Connection to Kotak server 'wss://mlhsm.kotaksecurities.com';

/**
 * Connects to the Kot closed.');
    clientSocket = null;
  });
};

/**
 * Subscribesak Market Feed WebSocket.
 */
function connectToKotak() {
  const { accessToken, ucc to a specific instrument token.
 * @param {string} token - The numeric instrument token to subscribe to.
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

} = tokenManager.getTokens(); // Assuming UCC is stored during auth
if (!accessToken || !ucc) */
const subscribeToToken = (token) => {
if (clientSocket && clientSocket.readyState === WebSocket.OPEN) {
// The subscription message format must match what the API expects.
// This is a {
console.error('[KotakWS] Cannot connect. Missing access token or UCC.');
return;
}

Generated code
kotakWebSocket = new WebSocket(KOTAK_WS_URL, {
    headers: {
      'Authorization common format, but may need adjustment based on specific docs.
    const subscriptionMessage = JSON.stringify({
      action: '': `Bearer ${accessToken}`,
      'neo-fin-key': 'neotradeapi', // As per documentation
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

subscribe',
tokens: [token],
});
clientSocket.send(subscriptionMessage);
console.log([WebSocket] Sent subscription request for token: ${token});
} else {
console.error('[WebSocket 'ucc': ucc,
}
});

Generated code
kotakWebSocket.on('open', () => {
    ] Cannot subscribe. Socket is not open.');
  }
};

/**
 * Unsubconsole.log('[KotakWS] Successfully connected to Kotak WebSocket server.');
  });

  kotakWebSocket.onscribes from a specific instrument token.
 * @param {string} token - The numeric instrument token to unsubscribe('message', (data) => {
    const message = data.toString();
    // Forward the message directly from.
 */
const unsubscribeFromToken = (token) => {
     if (clientSocket && clientSocket.readyState === WebSocket.OPEN) {
        const unsubscriptionMessage = JSON.stringify({
          action: 'unsubscribe to our connected frontend client
    if (clientWebSocket && clientWebSocket.readyState === WebSocket.OPEN) {
      client',
          tokens: [token],
        });
        clientSocket.send(unsubscriptionMessage);
        console.logWebSocket.send(message);
    }
  });

  kotakWebSocket.on('error', (error) => {(`[WebSocket] Sent unsubscribe request for token: ${token}`);
     }
};


/**
 * Cl
    console.error('[KotakWS] WebSocket Error:', error);
  });

  kotakWebSocket.oses the connection to the Kotak server.
 */
const disconnect = () => {
  if (clientSocket)on('close', () => {
    console.log('[KotakWS] Connection to Kotak closed.');
    kot {
    clientSocket.close();
  }
};

module.exports = {
  connectakWebSocket = null;
  });
}

/**
 * Subscribes to tick data for a specific,
  disconnect,
  subscribeToToken,
  unsubscribeFromToken,
};
``` instrument token.
 * @param {string} instrumentToken The numeric token to subscribe to.
 */
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

function subscribeToToken(instrumentToken) {
if (!kotakWebSocket || kotakWebSocket.readyState !== WebSocket.OPEN)4. Integrate WebSocket Logic into server.js

Generated code
We need to upgrade our Express server to also {
    console.warn('[KotakWS] Not connected. Cannot subscribe.');
    return;
  }

   handle WebSocket connections from our own frontend.

**Modify the file: `src/server.js`**
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

// First, unsubscribe from any previous token
if (currentSubscribedToken) {
const unsubscribeMsg = { action ```javascript
// src/server.js
// ... (keep all top-level imports)
const http: 'unsubscribe', tokens: [currentSubscribedToken] };
kotakWebSocket.send(JSON.stringify = require('http'); // <-- Add http module
const { WebSocketServer } = require('ws'); // <--(unsubscribeMsg));
console.log([KotakWS] Unsubscribed from ${currentSubscribedToken}); Add WebSocketServer
const kotakWsService = require('./services/kotakWebsocketService');

Generated code
const app = express();
  }
  
  // Subscribe to the new token
  const subscribeMsg = { action: 'subscribe
const server = http.createServer(app); // <-- Create an HTTP server from our Express app
const w', tokens: [instrumentToken] };
  kotakWebSocket.send(JSON.stringify(subscribeMsg));
  currentss = new WebSocketServer({ server }); // <-- Attach the WebSocket server to the HTTP server

const PORT = process.envSubscribedToken = instrumentToken;
  console.log(`[KotakWS] Subscribed to new token: ${instrumentToken.PORT || 3001;

// ... (keep all Express middleware and API routes: /login, /}`);
}

/**
 * Initializes the WebSocket server that listens for our frontend connection.
 * @paramoption-chain, etc.)

// --- WebSocket Server Logic ---
wss.on('connection', (frontend {http.Server} server - The Express HTTP server instance.
 */
function initializeOurServer(server) {
  const wss = new WebSocket.Server({ server });
  
  wss.on('connection', (wsSocket) => {
  console.log('[Backend WS] A frontend client has connected!');

  // When our) => {
    console.log('[BackendWS] Frontend client connected.');
    clientWebSocket = ws;

    // frontend sends a message to our backend...
  frontendSocket.on('message', (message) => {
     When the frontend sends a message (e.g., a token to subscribe to)
    ws.on('messageconst data = JSON.parse(message);
    console.log('[Backend WS] Received message from frontend:', data', (message) => {
      try {
        const data = JSON.parse(message);
        if);

    if (data.action === 'subscribe') {
      // Tell the Kotak service to subscribe to this (data.action === 'subscribe' && data.token) {
          subscribeToToken(data.token); new token
      kotakWsService.subscribeToToken(data.token);
    }
    if (data
        }
      } catch (e) {
        console.error('[BackendWS] Invalid message from client.action === 'unsubscribe') {
        kotakWsService.unsubscribeFromToken(data.token);
    :', message);
      }
    });

    ws.on('close', () => {
      console.log('[}
  });

  frontendSocket.on('close', () => {
    console.log('[Backend WS] Frontend clientBackendWS] Frontend client disconnected.');
      clientWebSocket = null;
    });
  });
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

disconnected.');
});
});

Generated code
// --- Kotak WebSocket Client Logic ---
// This is the callback    module.exports = {
  initializeOurServer,
  connectToKotak,
};
```
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

that runs every time Kotak sends us a new tick
const handleIncomingTick = (tickMessage) => {

Part 5.2: Backend - Integrating the WebSocket Server into server.js

We need to modify // Now, we need to broadcast this tick to all connected frontend clients
wss.clients.forEach((frontendserver.js to start our WebSocket server alongside the Express server.

Modify the file: src/server.Client) => { if (frontendClient.readyState === WebSocket.OPEN) { frontendClient.send(js

Generated javascript
// src/server.js
const http = require('http'); // Import thetickMessage); // Forward the raw message
            }
        });
    };

    // We connect to Kot built-in http module
// ... (keep other imports)
const kotakWSService = require('./services/kotakak's feed when the server starts.
    // In a real app, you might connect only after a userWebSocketService');

const app = express();
const server = http.createServer(app); // Create an HTTP server from our logs in.
    kotakWsService.connect(handleIncomingTick);


    // --- Server Start (Modified Express app
const PORT = process.env.PORT || 3001;

// ... (keep middleware) ---
    // We now listen on the http server, not the express app directly
    server.listen(PORT, () and all existing REST API routes: /login, /option-chain, etc.)

// --- WebSocket Initialization ---
// After => {
      console.log(`🚀 Backend Server (HTTP & WS) is online on http://localhost:${PORT}`);
     the login route, add the connection logic.
app.post('/api/auth/login', async (req, res) =>});
    ```

---

### **Part 5.2: Frontend - The Real-Time Ticker Component**

 {
  try {
    // ... (existing login logic)
    await kotakAuth.generateTradingToken(clientToken, viewToken, sid);
    
    // --- NEW: Connect to Kotak's WebSocket after aNow, we'll create a new React component specifically for displaying the live ticking price.

1.  **Create a new file: `src/components/LiveTicker.jsx`**

    ```javascript
    // src/components successful login ---
    kotakWSService.connectToKotak();

    res.status(200).json({ success: true, message: 'Login successful!' });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }/LiveTicker.jsx
    import React, { useState, useEffect, useRef } from 'react';

    // The WebSocket URL for our backend server
    const BACKEND_WS_URL = 'ws://localhost:3001';
});

// --- Server Start (Modified) ---
// We start our custom WebSocket server and make it listen on the

    function LiveTicker({ instrumentToken }) {
      const [livePrice, setLivePrice] = useState(' same port
kotakWSService.initializeOurServer(server);

server.listen(PORT, () => {Connecting...');
      const [lastPrice, setLastPrice] = useState(0);
      const [priceClass
  console.log(`🚀 Kotak API Backend (HTTP + WebSocket) is online on http://localhost:${PORT}`);
});, setPriceClass] = useState('');
      
      const socketRef = useRef(null);

      useEffect(() => {
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
Part 5.3: Frontend - The Tick-by-Tick Display Component

We will if (!instrumentToken) return;

Generated code
// Establish connection to our backend's WebSocket server
    socketRef. create a new component on the frontend specifically for selecting a strike and displaying its live price.
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

**Modify the file: current = new WebSocket(BACKEND_WS_URL);

Generated code
socketRef.current.onopen = () => {`src/App.jsx`**
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END
Generated javascript
// src/App.jsx
import React, { useState, useEffect, useRef
          console.log('[Frontend WS] Connected to backend server.');
          // Once connected, tell the backend which } from 'react';

// --- NEW TickByTick Component ---
function TickByTickViewer() {
  const token we want to subscribe to
          const message = { action: 'subscribe', token: instrumentToken };
          socket [expiry, setExpiry] = useState('');
  const [strikes, setStrikes] = useState([]);
  constRef.current.send(JSON.stringify(message));
          setLivePrice('Subscribed...');
        };

        // [selectedStrike, setSelectedStrike] = useState('');
  const [livePrice, setLivePrice] = useState( This is where we receive the tick-by-tick data pushed from our backend
        socketRef.current.onmessage = (0);
  const [priceClass, setPriceClass] = useState('');
  const wsRef = useRef(event) => {
            // NOTE: The exact structure of `parsedTick` depends on what Kotak sends.null);

  // Effect to manage the WebSocket connection
  useEffect(() => {
    // Connect to our backend's WebSocket server
    wsRef.current = new WebSocket('ws://localhost:3001');

    wsRef.current.
            // You MUST log the raw `event.data` to find the correct structure.
            // Let's assumeonopen = () => console.log('[FrontendWS] Connected to backend server.');
    
    // This is where we receive a common format for now.
            const parsedTick = JSON.parse(event.data);
            
            // Assuming the tick data from our backend
    wsRef.current.onmessage = (event) => {
      const data the tick data looks like: { tk: 'TOKEN', lp: 'PRICE' }
            if (parsedTick. = JSON.parse(event.data);
      if (data.type === 'tk' && data.lttk === instrumentToken) {
                const newPrice = parseFloat(parsedTick.lp);
                
                // Logicp) { // 'tk' is a typical tick message type
        setLivePrice(prevPrice => {
           to set color flash on price change
                if (newPrice > lastPrice) setPriceClass('price-up');if (data.ltp > prevPrice) setPriceClass('price-up');
          else if (data
                else if (newPrice < lastPrice) setPriceClass('price-down');
                
                set.ltp < prevPrice) setPriceClass('price-down');
          // Reset animation class after a momentLivePrice(newPrice.toFixed(2));
                setLastPrice(newPrice);
                
                //
          setTimeout(() => setPriceClass(''), 500);
          return data.ltp;
         Reset the class after animation
                setTimeout(() => setPriceClass(''), 700);
            }
        });
      }
    };

    wsRef.current.onclose = () => console.log('[Frontend};

        socketRef.current.onerror = (error) => {
          console.error('[Frontend WS]WS] Disconnected from backend server.');

    // Cleanup on component unmount
    return () => wsRef.current. Error:', error);
          setLivePrice('Error');
        };

        // Cleanup function: This is CRITICAL to prevent memory leaks.
        return () => {
          if (socketRef.current) {
            // Tellclose();
  }, []);

  const handleSubscribe = () => {
    // Here you would add logic to get the numeric token for the selected strike/expiry
    // For this example, we'll assume a function `getToken the backend to unsubscribe from this token
            const message = { action: 'unsubscribe', token: instrumentToken };
            socketRef.current.send(JSON.stringify(message));
            socketRef.current.close();
            console.log((expiry, selectedStrike)` exists.
    const tokenToSubscribe = 'YOUR_LOGIC_TO_GET_TOKEN'; // e.g., using scripMasterService
    
    if (wsRef.current.readyState === WebSocket.`[Frontend WS] Disconnected and unsubscribed from ${instrumentToken}`);
          }
        };
      }, [instrumentToken]); // This effect re-runs if the user selects a new instrumentToken

      return (
        <div className={OPEN) {
      wsRef.current.send(JSON.stringify({ action: 'subscribe', token: tokenToSubscribe`live-ticker ${priceClass}`}>
          {livePrice}
        </div>
      );
    }

    export }));
    }
  };
  
  return (
    <div className="tick-viewer">
      <h3>Live Tick Data</h3>
      <div className="controls">
        <input type="text" placeholder="Expiry Y default LiveTicker;
    ```

2.  **Integrate the `LiveTicker` into `App.jsx`**YYYMMDD" value={expiry} onChange={e => setExpiry(e.target.value)} />


    We'll modify our main `TradingView` to manage the selected strike and pass the token to our new `LiveTicker` component.

    **Modify `src/App.jsx`**

    ```javascript
    // src/App.jsx        {/* In a real app, this dropdown would be populated dynamically */}
        <select value={selectedStrike} onChange={e => setSelectedStrike(e.target.value)}>
          <option value="25000">2
    // ...
    import LiveTicker from './components/LiveTicker'; // <-- Import the new component

    function TradingView({ onLogout }) {
      // ... (keep state for instrument, indexPrice, optionChain, etc5000 CE</option>
          <option value="25100">25100 CE</option>
        </select>
        <button onClick={handleSubscribe}>Subscribe</button>
      </div>
      <div className={`live-price ${priceClass}`}>
        {livePrice || '---..)

      // --- New state for the selected instrument token ---
      const [selectedToken, setSelectedToken] =---'}
      </div>
    </div>
  );
}

// In your main TradingView component, you can now useState(null);

      // ... (handleFetchOptionChain remains the same)

      return (
        <div className add this new component
function TradingView({ onLogout }) {
    // ...
    return (
        <div className="="session-view">
          {/* ... (Index Selector and Price Display) ... */}
          {/* ...session-view">
            {/* ... your existing option chain table ... */}
            <hr/>
            <Tick (Data Controls Form for Expiry) ... */}
          
          <div className="main-display">
            {ByTickViewer />
            <button onClick={onLogout} className="logout-btn">Logout</button>
        </div>/* Option Chain Table */}
            <div className="option-chain-container">
              {optionChain.length > 0 && (
                <table>
                  <thead>...</thead>
                  <tbody>
                    {optionChain.map(({ strike })
    );
}

// ... the rest of your App.jsx
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END
**How It All Works Together => (
Generated code
<tr key={strike} onClick={() => {
                    // When a user clicks a row, find the token and set it
                    const ceToken = scripMasterService.getOptionToken(underlyingSymbol, expiry**
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

Login: The user logs in. Your backend authenticates with Kotak and, crucially, establishes the persistent WebSocket connection to wss://mlhsm.kotaksecurities.com.

*Frontend ConnectDate, strike, 'CE');
setSelectedToken(ceToken);
}}>
{/ ... table cells ... */}
</tr>
))}
</tbody>
</table>
)}
</div>

Generated code
{s:** The `TickByTickViewer` component mounts and opens its own WebSocket connection to *your* backend server at `ws/* Live Ticker Display */}
    <div className="live-ticker-container">
      <h3>Live Price</h3>
      {://localhost:3001`.
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

User Subscribes: The user selects an expiry/selectedToken ? (
<LiveTicker instrumentToken={selectedToken} />
) : (
<strike and clicks "Subscribe".

Subscription Message: The frontend sends a JSON message like {"action":div className="live-ticker-placeholder">Select a strike from the option chain to see its live price.</div> )} "subscribe", "token": "65586"} to your backend server.

**Backend Rel </div>
</div>

Generated code
<button onClick={onLogout} className="logout-btn">Logout</button>
ays Subscription:** Your `kotakWebSocketService` receives this message and relays the subscription request to the actual Kotak WebSocket server</div>
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END

);
}
// ...

Generated code
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
IGNORE_WHEN_COPYING_END
Verification and Debugging

.

Tick Data Streams In: Kotak's server now starts pushing tick-by-tick dataRestart both servers.

Log in and fetch the option chain for a specific expiry.

for that token to your backend.

Backend Relays Ticks: Your backend receives these ticks and immediately forwardsClick on a row in the option chain (e.g., the 25200 CE). them to the connected frontend client.

Frontend Displays Ticks: The TickByTickViewer component's

Monitor the Backend Terminal:

You should see [Backend WS] A frontend client has connected!.

You should see [Backend WS] Received message from frontend: { action: 'subscribeonmessagehandler fires for every tick, updating the state and causing the price on the screen to update in true', token: 'TOKEN_FOR_25200_CE' }.

You should see real-time.

This architecture provides the low-latency, real-time experience required for serious trading applications.