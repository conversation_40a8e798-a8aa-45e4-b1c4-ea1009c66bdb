// Debug script using exact same code as the server
const kotakAuth = require('./src/services/kotakAuthService');

async function debugAuth() {
  console.log('🔍 Testing authentication service directly...\n');
  
  try {
    const token = await kotakAuth.generateClientToken();
    console.log('✅ SUCCESS! Token generated:', token.slice(0, 50) + '...');
  } catch (error) {
    console.log('❌ FAILED!');
    console.log('Error:', error.message);
  }
}

debugAuth();
