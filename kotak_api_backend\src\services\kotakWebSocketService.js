// src/services/kotakWebSocketService.js
const WebSocket = require('ws');
const tokenManager = require('./tokenManager');

let clientWebSocket = null;  // This will be the connection TO our frontend
let kotakWebSocket = null;   // This will be the connection TO Kotak's server
let currentSubscribedToken = null;

const KOTAK_WS_URL = 'wss://mlhsm.kotaksecurities.com';

/**
 * Connects to the Kotak Market Feed WebSocket.
 */
function connectToKotak() {
  const { accessToken, ucc } = tokenManager.getTokens(); // Assuming UCC is stored during auth
  if (!accessToken || !ucc) {
    console.error('[KotakWS] Cannot connect. Missing access token or UCC.');
    return;
  }

  kotakWebSocket = new WebSocket(KOTAK_WS_URL, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'neo-fin-key': 'neotradeapi', // As per documentation
      'ucc': ucc,
    }
  });

  kotakWebSocket.on('open', () => {
    console.log('[KotakWS] Successfully connected to Kotak WebSocket server.');
  });

  kotakWebSocket.on('message', (data) => {
    const message = data.toString();
    // Forward the message directly to our connected frontend client
    if (clientWebSocket && clientWebSocket.readyState === WebSocket.OPEN) {
      clientWebSocket.send(message);
    }
  });

  kotakWebSocket.on('error', (error) => {
    console.error('[KotakWS] WebSocket Error:', error);
  });

  kotakWebSocket.on('close', () => {
    console.log('[KotakWS] Connection to Kotak closed.');
    kotakWebSocket = null;
  });
}

/**
 * Subscribes to tick data for a specific instrument token.
 * @param {string} instrumentToken The numeric token to subscribe to.
 */
function subscribeToToken(instrumentToken) {
  if (!kotakWebSocket || kotakWebSocket.readyState !== WebSocket.OPEN) {
    console.warn('[KotakWS] Not connected. Cannot subscribe.');
    return;
  }

  // First, unsubscribe from any previous token
  if (currentSubscribedToken) {
    const unsubscribeMsg = { action: 'unsubscribe', tokens: [currentSubscribedToken] };
    kotakWebSocket.send(JSON.stringify(unsubscribeMsg));
    console.log(`[KotakWS] Unsubscribed from ${currentSubscribedToken}`);
  }
  
  // Subscribe to the new token
  const subscribeMsg = { action: 'subscribe', tokens: [instrumentToken] };
  kotakWebSocket.send(JSON.stringify(subscribeMsg));
  currentSubscribedToken = instrumentToken;
  console.log(`[KotakWS] Subscribed to new token: ${instrumentToken}`);
}

/**
 * Initializes the WebSocket server that listens for our frontend connection.
 * @param {http.Server} server - The Express HTTP server instance.
 */
function initializeOurServer(server) {
  const wss = new WebSocket.Server({ server });
  
  wss.on('connection', (ws) => {
    console.log('[BackendWS] Frontend client connected.');
    clientWebSocket = ws;

    // When the frontend sends a message (e.g., a token to subscribe to)
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        if (data.action === 'subscribe' && data.token) {
          subscribeToToken(data.token);
        }
      } catch (e) {
        console.error('[BackendWS] Invalid message from client:', message);
      }
    });

    ws.on('close', () => {
      console.log('[BackendWS] Frontend client disconnected.');
      clientWebSocket = null;
    });
  });
}

module.exports = {
  initializeOurServer,
  connectToKotak,
};
