// src/server.js

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const tokenManager = require('./services/tokenManager');
const kotakAuth = require('./services/kotakAuthService');

const app = express();
const PORT = process.env.PORT || 3001;

// --- Middleware Setup ---
app.use(cors()); // Allows requests from our frontend
app.use(express.json()); // Parses incoming JSON requests

// --- API Routes ---

/**
 * POST /api/auth/login
 * A single endpoint to perform the entire 3-step login.
 * The frontend only needs to provide the TOTP.
 * Body: { "totp": "123456" }
 */
app.post('/api/auth/login', async (req, res) => {
  const { totp } = req.body;
  if (!totp) {
    return res.status(400).json({ success: false, message: 'TOTP is required in the request body.' });
  }

  try {
    console.log(`\n--- Received Login Request with TOTP: ${totp} ---`);
    const clientToken = await kotakAuth.generateClientToken();
    const { viewToken, sid } = await kotakAuth.generateViewToken(clientToken, totp);
    await kotakAuth.generateTradingToken(clientToken, viewToken, sid);
    
    res.status(200).json({ success: true, message: 'Login successful!' });
  } catch (error) {
    // The specific error message from the failed step will be sent.
    res.status(500).json({ success: false, message: error.message });
  }
});

/**
 * GET /api/auth/status
 * Allows the frontend to check if the server holds a valid session.
 */
app.get('/api/auth/status', (req, res) => {
  const { tradingToken } = tokenManager.getTokens();
  res.json({ isLoggedIn: !!tradingToken }); // !! converts value to boolean
});

/**
 * POST /api/auth/logout
 * Clears the tokens on the server.
 */
app.post('/api/auth/logout', (req, res) => {
  tokenManager.clearTokens();
  res.json({ success: true, message: 'Logged out successfully.' });
});


// --- Server Start ---
app.listen(PORT, () => {
  console.log(`🚀 Kotak API Backend Server is online and listening on http://localhost:${PORT}`);
});
