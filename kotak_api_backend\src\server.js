// src/server.js

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const tokenManager = require('./services/tokenManager');
const kotakAuth = require('./services/kotakAuthService');
const scripMasterService = require('./services/scripMasterService'); // <-- Keep for Step 4 option trading
const nseDataService = require('./services/nseDataService'); // <-- Add NSE service for index prices

const app = express();
const PORT = process.env.PORT || 3001;

// --- Middleware ---
app.use(cors());
app.use(express.json());

// --- Scrip Master Initialization Flag (for Step 4) ---
let isScripMasterInitialized = false; // Flag to ensure we only initialize once per session

// --- API Routes ---

/**
 * GET /
 * Root endpoint to confirm server is running
 */
app.get('/', (req, res) => {
  res.json({
    message: 'Kotak API Backend Server is running!',
    endpoints: {
      login: 'POST /api/auth/login',
      status: 'GET /api/auth/status',
      logout: 'POST /api/auth/logout'
    }
  });
});

/**
 * POST /api/auth/login
 * A single endpoint to perform the entire 3-step login.
 * The frontend only needs to provide the TOTP.
 * Body: { "totp": "123456" }
 */
app.post('/api/auth/login', async (req, res) => {
  const { totp } = req.body;
  console.log('\n🔍 === LOGIN REQUEST DEBUG ===');
  console.log('Request body:', req.body);
  console.log('TOTP received:', totp);
  console.log('Environment check:');
  console.log('- KOTAK_CONSUMER_KEY:', process.env.KOTAK_CONSUMER_KEY);
  console.log('- KOTAK_CONSUMER_SECRET:', process.env.KOTAK_CONSUMER_SECRET ? '***' + process.env.KOTAK_CONSUMER_SECRET.slice(-4) : 'NOT SET');
  console.log('- KOTAK_MOBILE_NUMBER:', process.env.KOTAK_MOBILE_NUMBER);
  console.log('- KOTAK_UCC:', process.env.KOTAK_UCC);
  console.log('- KOTAK_MPIN:', process.env.KOTAK_MPIN);

  if (!totp) {
    console.log('❌ No TOTP provided');
    return res.status(400).json({ success: false, message: 'TOTP is required in the request body.' });
  }

  try {
    console.log(`\n--- Starting Authentication Flow with TOTP: ${totp} ---`);
    console.log('Step 1: Generating Client Token...');
    const clientToken = await kotakAuth.generateClientToken();
    console.log('✅ Client Token Success:', clientToken.slice(0, 50) + '...');

    console.log('Step 2: Generating View Token...');
    const { viewToken, sid } = await kotakAuth.generateViewToken(clientToken, totp);
    console.log('✅ View Token Success');

    console.log('Step 3: Generating Trading Token...');
    await kotakAuth.generateTradingToken(clientToken, viewToken, sid);
    console.log('✅ All steps completed successfully!');

    // *** NEW: INITIALIZE F&O DATA SERVICE FOR STEP 4 ***
    if (!isScripMasterInitialized) {
      await scripMasterService.initializeFNOData(clientToken);
      isScripMasterInitialized = true;
    }

    res.status(200).json({ success: true, message: 'Login and services initialization successful!' });
  } catch (error) {
    console.log('❌ Authentication failed at some step');
    console.log('Error details:', error.message);
    console.log('Full error:', error);
    // The specific error message from the failed step will be sent.
    res.status(500).json({ success: false, message: error.message });
  }
});

/**
 * GET /api/auth/status
 * Allows the frontend to check if the server holds a valid session.
 */
app.get('/api/auth/status', (req, res) => {
  const { tradingToken } = tokenManager.getTokens();
  res.json({ isLoggedIn: !!tradingToken }); // !! converts value to boolean
});

/**
 * POST /api/auth/logout
 * Clears the tokens on the server.
 */
app.post('/api/auth/logout', (req, res) => {
  tokenManager.clearTokens();
  isScripMasterInitialized = false; // Reset flag on logout
  res.json({ success: true, message: 'Logged out successfully.' });
});

/**
 * GET /api/debug/env
 * Debug endpoint to check environment variables
 */
app.get('/api/debug/env', (req, res) => {
  res.json({
    hasConsumerKey: !!process.env.KOTAK_CONSUMER_KEY,
    hasConsumerSecret: !!process.env.KOTAK_CONSUMER_SECRET,
    consumerKeyLength: process.env.KOTAK_CONSUMER_KEY ? process.env.KOTAK_CONSUMER_KEY.length : 0,
    consumerSecretLength: process.env.KOTAK_CONSUMER_SECRET ? process.env.KOTAK_CONSUMER_SECRET.length : 0,
    consumerKeyPreview: process.env.KOTAK_CONSUMER_KEY ? process.env.KOTAK_CONSUMER_KEY.slice(0, 10) + '...' : 'NOT SET',
    consumerSecretPreview: process.env.KOTAK_CONSUMER_SECRET ? '***' + process.env.KOTAK_CONSUMER_SECRET.slice(-4) : 'NOT SET',
    nodeEnv: process.env.NODE_ENV,
    port: process.env.PORT
  });
});

/**
 * GET /api/data/index-ltp
 * A protected route to fetch the Last Traded Price (LTP) for a given index.
 * Uses NSE public API for reliable index data while keeping Kotak authentication for future option trading.
 * Example: /api/data/index-ltp?instrument=NIFTY_50
 */
app.get('/api/data/index-ltp', async (req, res) => {
  console.log(`\n--- Received Index Price Request ---`);

  // We still protect this route to ensure only logged-in users can access it.
  const { tradingToken } = tokenManager.getTokens();
  if (!tradingToken) {
    return res.status(401).json({ success: false, message: 'Unauthorized. Please log in first.' });
  }

  const { instrument } = req.query; // e.g., 'NIFTY_50'

  // Map our internal name to the exact string the NSE API expects.
  const indexNameMap = {
    'NIFTY_50': 'NIFTY 50',
    'BANK_NIFTY': 'NIFTY BANK'
  };
  const nseIndexName = indexNameMap[instrument];

  if (!nseIndexName) {
    return res.status(400).json({ success: false, message: `Invalid instrument name: ${instrument}` });
  }

  try {
    // Call our new, reliable NSE service.
    const ltp = await nseDataService.getIndexLtp(nseIndexName);
    res.json({ success: true, ltp: ltp });
  } catch (error) {
    // If the NSE service fails, forward its error message to the frontend.
    res.status(503).json({ success: false, message: error.message });
  }
});


// --- Server Start ---
console.log('Starting server...');
app.listen(PORT, () => {
  console.log(`🚀 Kotak API Backend Server is online and listening on http://localhost:${PORT}`);
  console.log('Server started successfully!');
});
