// src/server.js

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const tokenManager = require('./services/tokenManager');
const kotakAuth = require('./services/kotakAuthService');

const app = express();
const PORT = process.env.PORT || 3001;

// --- Middleware Setup ---
app.use(cors()); // Allows requests from our frontend
app.use(express.json()); // Parses incoming JSON requests

// --- API Routes ---

/**
 * GET /
 * Root endpoint to confirm server is running
 */
app.get('/', (req, res) => {
  res.json({
    message: 'Kotak API Backend Server is running!',
    endpoints: {
      login: 'POST /api/auth/login',
      status: 'GET /api/auth/status',
      logout: 'POST /api/auth/logout'
    }
  });
});

/**
 * POST /api/auth/login
 * A single endpoint to perform the entire 3-step login.
 * The frontend only needs to provide the TOTP.
 * Body: { "totp": "123456" }
 */
app.post('/api/auth/login', async (req, res) => {
  const { totp } = req.body;
  console.log('\n🔍 === LOGIN REQUEST DEBUG ===');
  console.log('Request body:', req.body);
  console.log('TOTP received:', totp);
  console.log('Environment check:');
  console.log('- KOTAK_CONSUMER_KEY:', process.env.KOTAK_CONSUMER_KEY);
  console.log('- KOTAK_CONSUMER_SECRET:', process.env.KOTAK_CONSUMER_SECRET ? '***' + process.env.KOTAK_CONSUMER_SECRET.slice(-4) : 'NOT SET');
  console.log('- KOTAK_MOBILE_NUMBER:', process.env.KOTAK_MOBILE_NUMBER);
  console.log('- KOTAK_UCC:', process.env.KOTAK_UCC);
  console.log('- KOTAK_MPIN:', process.env.KOTAK_MPIN);

  if (!totp) {
    console.log('❌ No TOTP provided');
    return res.status(400).json({ success: false, message: 'TOTP is required in the request body.' });
  }

  try {
    console.log(`\n--- Starting Authentication Flow with TOTP: ${totp} ---`);
    console.log('Step 1: Generating Client Token...');
    const clientToken = await kotakAuth.generateClientToken();
    console.log('✅ Client Token Success:', clientToken.slice(0, 50) + '...');

    console.log('Step 2: Generating View Token...');
    const { viewToken, sid } = await kotakAuth.generateViewToken(clientToken, totp);
    console.log('✅ View Token Success');

    console.log('Step 3: Generating Trading Token...');
    await kotakAuth.generateTradingToken(clientToken, viewToken, sid);
    console.log('✅ All steps completed successfully!');

    res.status(200).json({ success: true, message: 'Login successful!' });
  } catch (error) {
    console.log('❌ Authentication failed at some step');
    console.log('Error details:', error.message);
    console.log('Full error:', error);
    // The specific error message from the failed step will be sent.
    res.status(500).json({ success: false, message: error.message });
  }
});

/**
 * GET /api/auth/status
 * Allows the frontend to check if the server holds a valid session.
 */
app.get('/api/auth/status', (req, res) => {
  const { tradingToken } = tokenManager.getTokens();
  res.json({ isLoggedIn: !!tradingToken }); // !! converts value to boolean
});

/**
 * POST /api/auth/logout
 * Clears the tokens on the server.
 */
app.post('/api/auth/logout', (req, res) => {
  tokenManager.clearTokens();
  res.json({ success: true, message: 'Logged out successfully.' });
});

/**
 * GET /api/debug/env
 * Debug endpoint to check environment variables
 */
app.get('/api/debug/env', (req, res) => {
  res.json({
    hasConsumerKey: !!process.env.KOTAK_CONSUMER_KEY,
    hasConsumerSecret: !!process.env.KOTAK_CONSUMER_SECRET,
    consumerKeyLength: process.env.KOTAK_CONSUMER_KEY ? process.env.KOTAK_CONSUMER_KEY.length : 0,
    consumerSecretLength: process.env.KOTAK_CONSUMER_SECRET ? process.env.KOTAK_CONSUMER_SECRET.length : 0,
    consumerKeyPreview: process.env.KOTAK_CONSUMER_KEY ? process.env.KOTAK_CONSUMER_KEY.slice(0, 10) + '...' : 'NOT SET',
    consumerSecretPreview: process.env.KOTAK_CONSUMER_SECRET ? '***' + process.env.KOTAK_CONSUMER_SECRET.slice(-4) : 'NOT SET',
    nodeEnv: process.env.NODE_ENV,
    port: process.env.PORT
  });
});

/**
 * GET /api/data/index-ltp
 * A protected route to fetch the Last Traded Price (LTP) for a given index.
 * The frontend will specify the index via a query parameter.
 * Example: /api/data/index-ltp?instrument=NIFTY_50
 */
app.get('/api/data/index-ltp', async (req, res) => {
  console.log(`\n--- Received Market Data Request ---`);

  // 1. Check for Authentication
  // This route should only work if the user is logged in.
  const { tradingToken, accessToken } = tokenManager.getTokens();
  if (!tradingToken || !accessToken) {
    console.error('[Data API] Unauthorized access attempt. No session token found.');
    return res.status(401).json({ success: false, message: 'Unauthorized. Please log in first.' });
  }

  // 2. Get the instrument from the query parameter
  const { instrument } = req.query; // e.g., "NIFTY_50" or "BANK_NIFTY"
  if (!instrument) {
    return res.status(400).json({ success: false, message: 'Instrument query parameter is required.' });
  }

  // 3. Map the simple name to the official Kotak Neo Symbol
  const instrumentMap = {
    'NIFTY_50': 'nse_cm|NIFTY 50',
    'BANK_NIFTY': 'nse_cm|Nifty Bank'
  };
  const neoSymbol = instrumentMap[instrument];

  if (!neoSymbol) {
    return res.status(400).json({ success: false, message: 'Invalid instrument specified.' });
  }

  // 4. Call the Kotak Quotes API
  const quotesApiUrl = `https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/${encodeURIComponent(neoSymbol)}/ltp`;

  console.log(`[Data API] Fetching LTP for ${instrument} from ${quotesApiUrl}`);

  try {
    // IMPORTANT: The Quotes API uses the initial `accessToken` (Bearer token),
    // not the final trading session token.
    const response = await axios.get(quotesApiUrl, {
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    });

    // 5. Process the response and send it to the frontend
    if (response.data && response.data.length > 0) {
      const ltp = response.data[0].ltp;
      console.log(`[Data API] Successfully fetched LTP: ${ltp}`);
      res.json({ success: true, ltp: parseFloat(ltp) });
    } else {
      throw new Error('API returned an empty data array.');
    }
  } catch (error) {
    const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error('[Data API] FAILED:', errorMsg);
    res.status(500).json({ success: false, message: `Failed to fetch market data: ${errorMsg}` });
  }
});


// --- Server Start ---
console.log('Starting server...');
app.listen(PORT, () => {
  console.log(`🚀 Kotak API Backend Server is online and listening on http://localhost:${PORT}`);
  console.log('Server started successfully!');
});
