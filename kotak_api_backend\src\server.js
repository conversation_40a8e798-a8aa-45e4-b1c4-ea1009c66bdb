// src/server.js

require('dotenv').config();
const express = require('express');
const http = require('http'); // Import the built-in http module
const cors = require('cors');
const axios = require('axios');
const tokenManager = require('./services/tokenManager');
const kotakAuth = require('./services/kotakAuthService');
const scripMasterService = require('./services/scripMasterService'); // <-- Keep for Step 4 option trading
const nseDataService = require('./services/nseDataService'); // <-- Add NSE service for index prices
const kotakWSService = require('./services/kotakWebSocketService'); // <-- Add WebSocket service

const app = express();
const server = http.createServer(app); // Create an HTTP server from our Express app
const PORT = process.env.PORT || 3001;

// --- Middleware ---
app.use(cors());
app.use(express.json());

// --- Scrip Master Initialization Flag (for Step 4) ---
let isScripMasterInitialized = false; // Flag to ensure we only initialize once per session

// --- API Routes ---

/**
 * GET /
 * Root endpoint to confirm server is running
 */
app.get('/', (req, res) => {
  res.json({
    message: 'Kotak API Backend Server is running!',
    endpoints: {
      login: 'POST /api/auth/login',
      status: 'GET /api/auth/status',
      logout: 'POST /api/auth/logout'
    }
  });
});

/**
 * POST /api/auth/login
 * A single endpoint to perform the entire 3-step login.
 * The frontend only needs to provide the TOTP.
 * Body: { "totp": "123456" }
 */
app.post('/api/auth/login', async (req, res) => {
  const { totp } = req.body;
  console.log('\n🔍 === LOGIN REQUEST DEBUG ===');
  console.log('Request body:', req.body);
  console.log('TOTP received:', totp);
  console.log('Environment check:');
  console.log('- KOTAK_CONSUMER_KEY:', process.env.KOTAK_CONSUMER_KEY);
  console.log('- KOTAK_CONSUMER_SECRET:', process.env.KOTAK_CONSUMER_SECRET ? '***' + process.env.KOTAK_CONSUMER_SECRET.slice(-4) : 'NOT SET');
  console.log('- KOTAK_MOBILE_NUMBER:', process.env.KOTAK_MOBILE_NUMBER);
  console.log('- KOTAK_UCC:', process.env.KOTAK_UCC);
  console.log('- KOTAK_MPIN:', process.env.KOTAK_MPIN);

  if (!totp) {
    console.log('❌ No TOTP provided');
    return res.status(400).json({ success: false, message: 'TOTP is required in the request body.' });
  }

  try {
    console.log(`\n--- Starting Authentication Flow with TOTP: ${totp} ---`);
    console.log('Step 1: Generating Client Token...');
    const clientToken = await kotakAuth.generateClientToken();
    console.log('✅ Client Token Success:', clientToken.slice(0, 50) + '...');

    console.log('Step 2: Generating View Token...');
    const { viewToken, sid } = await kotakAuth.generateViewToken(clientToken, totp);
    console.log('✅ View Token Success');

    console.log('Step 3: Generating Trading Token...');
    await kotakAuth.generateTradingToken(clientToken, viewToken, sid);
    console.log('✅ All steps completed successfully!');

    // *** NEW: INITIALIZE F&O DATA SERVICE FOR STEP 4 ***
    if (!isScripMasterInitialized) {
      await scripMasterService.initializeFNOData(clientToken);
      isScripMasterInitialized = true;
    }

    // *** NEW: CONNECT TO KOTAK'S WEBSOCKET AFTER SUCCESSFUL LOGIN ***
    kotakWSService.connectToKotak();

    res.status(200).json({ success: true, message: 'Login and services initialization successful!' });
  } catch (error) {
    console.log('❌ Authentication failed at some step');
    console.log('Error details:', error.message);
    console.log('Full error:', error);
    // The specific error message from the failed step will be sent.
    res.status(500).json({ success: false, message: error.message });
  }
});

/**
 * GET /api/auth/status
 * Allows the frontend to check if the server holds a valid session.
 */
app.get('/api/auth/status', (req, res) => {
  const { tradingToken } = tokenManager.getTokens();
  res.json({ isLoggedIn: !!tradingToken }); // !! converts value to boolean
});

/**
 * POST /api/auth/logout
 * Clears the tokens on the server.
 */
app.post('/api/auth/logout', (req, res) => {
  tokenManager.clearTokens();
  isScripMasterInitialized = false; // Reset flag on logout
  res.json({ success: true, message: 'Logged out successfully.' });
});

/**
 * GET /api/debug/env
 * Debug endpoint to check environment variables
 */
app.get('/api/debug/env', (req, res) => {
  res.json({
    hasConsumerKey: !!process.env.KOTAK_CONSUMER_KEY,
    hasConsumerSecret: !!process.env.KOTAK_CONSUMER_SECRET,
    consumerKeyLength: process.env.KOTAK_CONSUMER_KEY ? process.env.KOTAK_CONSUMER_KEY.length : 0,
    consumerSecretLength: process.env.KOTAK_CONSUMER_SECRET ? process.env.KOTAK_CONSUMER_SECRET.length : 0,
    consumerKeyPreview: process.env.KOTAK_CONSUMER_KEY ? process.env.KOTAK_CONSUMER_KEY.slice(0, 10) + '...' : 'NOT SET',
    consumerSecretPreview: process.env.KOTAK_CONSUMER_SECRET ? '***' + process.env.KOTAK_CONSUMER_SECRET.slice(-4) : 'NOT SET',
    nodeEnv: process.env.NODE_ENV,
    port: process.env.PORT
  });
});

/**
 * GET /api/data/index-ltp
 * A protected route to fetch the Last Traded Price (LTP) for a given index.
 * Uses NSE public API for reliable index data while keeping Kotak authentication for future option trading.
 * Example: /api/data/index-ltp?instrument=NIFTY_50
 */
app.get('/api/data/index-ltp', async (req, res) => {
  console.log(`\n--- Received Index Price Request ---`);

  // We still protect this route to ensure only logged-in users can access it.
  const { tradingToken } = tokenManager.getTokens();
  if (!tradingToken) {
    return res.status(401).json({ success: false, message: 'Unauthorized. Please log in first.' });
  }

  const { instrument } = req.query; // e.g., 'NIFTY_50'

  // Map our internal name to the exact string the NSE API expects.
  const indexNameMap = {
    'NIFTY_50': 'NIFTY 50',
    'BANK_NIFTY': 'NIFTY BANK'
  };
  const nseIndexName = indexNameMap[instrument];

  if (!nseIndexName) {
    return res.status(400).json({ success: false, message: `Invalid instrument name: ${instrument}` });
  }

  try {
    // Call our new, reliable NSE service.
    const ltp = await nseDataService.getIndexLtp(nseIndexName);
    res.json({ success: true, ltp: ltp });
  } catch (error) {
    // If the NSE service fails, forward its error message to the frontend.
    res.status(503).json({ success: false, message: error.message });
  }
});

/**
 * GET /api/data/option-chain
 * Builds and returns the option chain data for a given instrument.
 * Query Params:
 *  - instrument: 'NIFTY_50' or 'BANK_NIFTY'
 *  - indexPrice: The current LTP of the index (e.g., 25149.85)
 */
app.get('/api/data/option-chain', async (req, res) => {
  console.log(`\n--- Received Option Chain Request ---`);

  const { accessToken } = tokenManager.getTokens();
  if (!accessToken) {
    return res.status(401).json({ success: false, message: 'Unauthorized.' });
  }

  const { instrument, indexPrice, expiry } = req.query;
  if (!instrument || !indexPrice || !expiry) {
    return res.status(400).json({ success: false, message: 'Instrument, indexPrice, and expiry are required.' });
  }

  try {
    // 1. Determine parameters based on the instrument
    const isNifty = instrument === 'NIFTY_50';
    const underlyingSymbol = isNifty ? 'NIFTY' : 'BANKNIFTY';
    const strikeDifference = isNifty ? 50 : 100;
    const atmStrike = Math.round(parseFloat(indexPrice) / strikeDifference) * strikeDifference;

    // ========================== USER-CONTROLLED EXPIRY ==========================
    // Use the expiry date provided by the user - much simpler and more reliable!
    const expiryDateString = expiry;
    console.log(`[Opt. Chain] Building chain for ${underlyingSymbol}, ATM: ${atmStrike}, User-selected Expiry: ${expiryDateString}`);
    // ========================================================================

    // 3. Generate strike prices and find their tokens
    const strikePrices = [];
    const neoSymbolsToFetch = [];
    for (let i = -5; i <= 5; i++) {
      const strike = atmStrike + (i * strikeDifference);
      strikePrices.push(strike);

      const ceToken = scripMasterService.getOptionToken(underlyingSymbol, expiryDateString, strike, 'CE');
      const peToken = scripMasterService.getOptionToken(underlyingSymbol, expiryDateString, strike, 'PE');

      if (ceToken) neoSymbolsToFetch.push(`nse_fo|${ceToken}`);
      if (peToken) neoSymbolsToFetch.push(`nse_fo|${peToken}`);
    }

    if (neoSymbolsToFetch.length === 0) {
      // This error message is now much more specific and useful
      throw new Error(`Could not find any option tokens for ${underlyingSymbol} with expiry ${expiryDateString}. This might happen on an expiry day after market hours.`);
    }

    // 4. Make a single, bulk API call to Kotak for all option prices
    const symbolsString = neoSymbolsToFetch.join(',');
    const quotesApiUrl = `https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/${encodeURIComponent(symbolsString)}/ltp`;

    console.log(`[Opt. Chain] Fetching prices for ${neoSymbolsToFetch.length} tokens...`);
    console.log(`[Opt. Chain] API URL: ${quotesApiUrl}`);
    console.log(`[Opt. Chain] Tokens being requested:`, neoSymbolsToFetch);

    const quotesResponse = await axios.get(quotesApiUrl, {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });

    // DEBUG: Log the actual API response
    console.log(`[Opt. Chain] Raw API Response:`, JSON.stringify(quotesResponse.data, null, 2));

    // 5. Map the results back to our strike prices
    const quotesMap = new Map();
    if (quotesResponse.data && Array.isArray(quotesResponse.data)) {
      quotesResponse.data.forEach((quote, index) => {
        console.log(`[Opt. Chain] Processing quote ${index}:`, quote);
        if (quote.exchange_token && quote.ltp) {
          quotesMap.set(quote.exchange_token, quote.ltp);
          console.log(`[Opt. Chain] Mapped: ${quote.exchange_token} -> ${quote.ltp}`);
        } else {
          console.warn(`[Opt. Chain] Quote missing exchange_token or ltp:`, quote);
        }
      });
    } else {
      console.warn(`[Opt. Chain] Unexpected response structure:`, quotesResponse.data);
    }

    const optionChain = strikePrices.map(strike => {
      const ceToken = scripMasterService.getOptionToken(underlyingSymbol, expiryDateString, strike, 'CE');
      const peToken = scripMasterService.getOptionToken(underlyingSymbol, expiryDateString, strike, 'PE');

      // DEBUG: Log token lookup and price mapping
      console.log(`[Opt. Chain] Strike ${strike}: CE token=${ceToken}, PE token=${peToken}`);
      console.log(`[Opt. Chain] Strike ${strike}: CE price=${ceToken && quotesMap.has(ceToken) ? quotesMap.get(ceToken) : 'NOT FOUND'}`);
      console.log(`[Opt. Chain] Strike ${strike}: PE price=${peToken && quotesMap.has(peToken) ? quotesMap.get(peToken) : 'NOT FOUND'}`);

      return {
        strike,
        ce_ltp: ceToken && quotesMap.has(ceToken) ? parseFloat(quotesMap.get(ceToken)) : 'N/A',
        pe_ltp: peToken && quotesMap.has(peToken) ? parseFloat(quotesMap.get(peToken)) : 'N/A',
      };
    });

    console.log(`[Opt. Chain] SUCCESS: Built option chain with ${optionChain.length} strikes`);
    res.json({ success: true, optionChain });

  } catch (error) {
    const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error('[Opt. Chain] FAILED:', errorMsg);
    res.status(500).json({ success: false, message: `Failed to build option chain: ${errorMsg}` });
  }
});


// --- WebSocket Server Initialization ---
kotakWSService.initializeOurServer(server);

// --- Server Start (Modified) ---
console.log('Starting server...');
server.listen(PORT, () => {
  console.log(`🚀 Kotak API Backend (HTTP + WebSocket) is online on http://localhost:${PORT}`);
  console.log('Server started successfully!');
});
