// src/services/kotakAuthService.js

const axios = require('axios');
const tokenManager = require('./tokenManager');
require('dotenv').config();

const {
  KOTAK_CONSUMER_KEY,
  KOTAK_CONSUMER_SECRET,
  KOTAK_MOBILE_NUMBER,
  KOTAK_UCC,
  KOTAK_MPIN,
} = process.env;

/**
 * STEP 1: Generates the initial client 'access_token'.
 * This token authorizes the application itself.
 */
const generateClientToken = async () => {
  console.log('[Auth] Step 1: Generating Client Access Token...');
  console.log('[Auth] Consumer Key:', KOTAK_CONSUMER_KEY);
  console.log('[Auth] Consumer Secret:', KOTAK_CONSUMER_SECRET ? '***' + KOTAK_CONSUMER_SECRET.slice(-4) : 'NOT SET');
  const credentials = `${KOTAK_CONSUMER_KEY}:${KOT<PERSON><PERSON>_CONSUMER_SECRET}`;
  const encodedAuth = Buffer.from(credentials).toString('base64');
  console.log('[Auth] Encoded Auth:', encodedAuth.slice(0, 20) + '...');

  try {
    console.log('[Auth] Making request to:', 'https://napi.kotaksecurities.com/oauth2/token');
    console.log('[Auth] Request headers:', {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${encodedAuth.slice(0, 20)}...`,
    });
    console.log('[Auth] Request body:', 'grant_type=client_credentials');

    const response = await axios.post(
      'https://napi.kotaksecurities.com/oauth2/token',
      'grant_type=client_credentials',
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${encodedAuth}`,
        },
        timeout: 15000, // 15 second timeout
      }
    );
    const { access_token } = response.data;
    if (!access_token) throw new Error('Client Access Token not found in API response.');
    
    tokenManager.setTokens({ accessToken: access_token });
    console.log('[Auth] Step 1: SUCCESS.');
    return access_token;
  } catch (error) {
    const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error('[Auth] Step 1 FAILED:', errorMsg);
    throw new Error(`Client Token Generation Failed: ${errorMsg}`);
  }
};

/**
 * STEP 2: Generates the 'View Token' using the TOTP.
 * This token authorizes viewing user data.
 */
const generateViewToken = async (accessToken, totp) => {
  console.log('[Auth] Step 2: Generating View Token...');
  try {
    const response = await axios.post(
      'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login',
      { mobileNumber: KOTAK_MOBILE_NUMBER, ucc: KOTAK_UCC, totp },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
          'neo-fin-key': 'neotradeapi',
        },
      }
    );
    const { token: viewToken, sid } = response.data.data;
    if (!viewToken || !sid) throw new Error('View Token or SID not found in API response.');

    tokenManager.setTokens({ viewToken, sid });
    console.log('[Auth] Step 2: SUCCESS.');
    return { viewToken, sid };
  } catch (error) {
    const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error('[Auth] Step 2 FAILED:', errorMsg);
    throw new Error(`View Token Generation Failed. Check your TOTP. Details: ${errorMsg}`);
  }
};

/**
 * STEP 3: Generates the final 'Trading Token' using the MPIN.
 * This is the final session token for making data requests.
 */
const generateTradingToken = async (accessToken, viewToken, sid) => {
  console.log('[Auth] Step 3: Generating Final Trading Token...');
  try {
    const response = await axios.post(
      'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate',
      { mpin: KOTAK_MPIN },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`, // The initial token
          'Auth': viewToken,                      // The token from step 2
          'sid': sid,                             // The session ID from step 2
          'neo-fin-key': 'neotradeapi',
        },
      }
    );
    const { token: tradingToken } = response.data.data;
    if (!tradingToken) throw new Error('Trading Token not found in API response.');

    tokenManager.setTokens({ tradingToken });
    console.log('[Auth] Step 3: SUCCESS. Login is complete!');
    return tradingToken;
  } catch (error) {
    const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error('[Auth] Step 3 FAILED:', errorMsg);
    throw new Error(`Trading Token Generation Failed. Check your MPIN. Details: ${errorMsg}`);
  }
};

module.exports = {
  generateClientToken,
  generateViewToken,
  generateTradingToken,
};
