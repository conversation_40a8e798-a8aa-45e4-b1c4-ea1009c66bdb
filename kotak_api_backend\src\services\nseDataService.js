// src/services/nseDataService.js

const axios = require('axios');

/**
 * Fetches real-time index prices from Yahoo Finance API
 * This is a reliable, free source for live market data
 *
 * @param {string} indexName - The exact name of the index, e.g., "NIFTY 50" or "NIFTY BANK".
 * @returns {Promise<number>} A promise that resolves to the numeric LTP of the index.
 */
const getIndexLtp = async (indexName) => {
  console.log(`[NSE Service] Fetching REAL market data for: "${indexName}"`);

  // Map index names to Yahoo Finance symbols
  const symbolMap = {
    'NIFTY 50': '^NSEI',      // Nifty 50 symbol on Yahoo Finance
    'NIFTY BANK': '^NSEBANK'  // Bank Nifty symbol on Yahoo Finance
  };

  const symbol = symbolMap[indexName];
  if (!symbol) {
    throw new Error(`Unsupported index: ${indexName}`);
  }

  try {
    // Yahoo Finance API endpoint for real-time quotes
    const url = `https://query1.finance.yahoo.com/v8/finance/chart/${symbol}`;

    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      },
      timeout: 10000
    });

    if (response.data && response.data.chart && response.data.chart.result && response.data.chart.result.length > 0) {
      const result = response.data.chart.result[0];
      const meta = result.meta;

      // Get the current price (regularMarketPrice or previousClose)
      const currentPrice = meta.regularMarketPrice || meta.previousClose;

      if (currentPrice) {
        console.log(`[NSE Service] SUCCESS: Real LTP for ${indexName} is ${currentPrice}`);
        return parseFloat(currentPrice);
      } else {
        throw new Error('Price data not found in response');
      }
    } else {
      throw new Error('Invalid response structure from Yahoo Finance');
    }

  } catch (error) {
    const errorMsg = error.response ? `Status ${error.response.status}` : error.message;
    console.error(`[NSE Service] FAILED to fetch real market data: ${errorMsg}`);
    throw new Error(`Could not fetch live index data: ${errorMsg}`);
  }
};

module.exports = {
  getIndexLtp,
};
