// src/services/nseDataService.js

const axios = require('axios');

// This is the public API endpoint that powers the charts on the nseindia.com website.
const NSE_INDEX_API_URL = 'https://www.nseindia.com/api/chart-databyindex?index=';

/**
 * Fetches the Last Traded Price (LTP) for a given index from the NSE's public API.
 * 
 * @param {string} indexName - The exact name of the index, e.g., "NIFTY 50" or "NIFTY BANK".
 * @returns {Promise<number>} A promise that resolves to the numeric LTP of the index.
 */
const getIndexLtp = async (indexName) => {
  console.log(`[NSE Service] Attempting to fetch LTP for: "${indexName}"`);
  
  // The NSE API URL requires spaces to be encoded as '%20'.
  const encodedIndexName = indexName.replace(/ /g, '%20');
  const fullUrl = `${NSE_INDEX_API_URL}${encodedIndexName}`;

  try {
    // CRITICAL: The NSE website will block requests that don't look like they come from a real browser.
    // We must provide a valid 'User-Agent' header to mimic a browser request.
    const response = await axios.get(fullUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept': 'application/json'
      },
    });

    // The data is located in the 'grapthData' property, which is an array of [timestamp, price] pairs.
    if (response.data && response.data.grapthData && response.data.grapthData.length > 0) {
      const lastDataPoint = response.data.grapthData[response.data.grapthData.length - 1];
      const ltp = lastDataPoint[1]; // The price is the second element (index 1).
      console.log(`[NSE Service] SUCCESS: Fetched LTP for ${indexName} is ${ltp}`);
      return ltp;
    } else {
      throw new Error('Invalid data structure in NSE API response. The "grapthData" array is missing or empty.');
    }
  } catch (error) {
    const errorMsg = error.response ? `Status ${error.response.status}` : error.message;
    console.error(`[NSE Service] FAILED to fetch from NSE public API: ${errorMsg}`);
    throw new Error(`Could not fetch live index data from the public endpoint. It may be temporarily down.`);
  }
};

module.exports = {
  getIndexLtp,
};
