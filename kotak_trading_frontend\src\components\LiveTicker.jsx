// src/components/LiveTicker.jsx
import React, { useState, useEffect, useRef } from 'react';

// The WebSocket URL for our backend server
const BACKEND_WS_URL = 'ws://localhost:3001';

function LiveTicker({ instrumentToken, instrumentName }) {
  const [livePrice, setLivePrice] = useState('Connecting...');
  const [lastPrice, setLastPrice] = useState(0);
  const [priceClass, setPriceClass] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  
  const socketRef = useRef(null);

  useEffect(() => {
    if (!instrumentToken) return;

    // Establish connection to our backend's WebSocket server
    socketRef.current = new WebSocket(BACKEND_WS_URL);

    socketRef.current.onopen = () => {
      console.log('[Frontend WS] Connected to backend server.');
      setIsConnected(true);
      
      // Once connected, tell the backend which token we want to subscribe to
      const message = { action: 'subscribe', token: instrumentToken };
      socketRef.current.send(JSON.stringify(message));
      setLivePrice('Subscribed...');
    };

    // This is where we receive the tick-by-tick data pushed from our backend
    socketRef.current.onmessage = (event) => {
      try {
        // NOTE: The exact structure of `parsedTick` depends on what Kotak sends.
        // You MUST log the raw `event.data` to find the correct structure.
        console.log('[Frontend WS] Raw tick data:', event.data);
        
        // Let's assume a common format for now.
        const parsedTick = JSON.parse(event.data);
        
        // Assuming the tick data looks like: { tk: 'TOKEN', lp: 'PRICE' }
        if (parsedTick.tk === instrumentToken) {
          const newPrice = parseFloat(parsedTick.lp);
          
          // Logic to set color flash on price change
          if (newPrice > lastPrice) setPriceClass('price-up');
          else if (newPrice < lastPrice) setPriceClass('price-down');
          
          setLivePrice(newPrice.toFixed(2));
          setLastPrice(newPrice);
          
          // Reset the class after animation
          setTimeout(() => setPriceClass(''), 700);
        }
      } catch (error) {
        console.error('[Frontend WS] Error parsing tick data:', error);
        console.log('[Frontend WS] Raw data was:', event.data);
      }
    };

    socketRef.current.onerror = (error) => {
      console.error('[Frontend WS] Error:', error);
      setLivePrice('Error');
      setIsConnected(false);
    };

    socketRef.current.onclose = () => {
      console.log('[Frontend WS] Disconnected from backend server.');
      setIsConnected(false);
      setLivePrice('Disconnected');
    };

    // Cleanup function: This is CRITICAL to prevent memory leaks.
    return () => {
      if (socketRef.current) {
        // Tell the backend to unsubscribe from this token
        const message = { action: 'unsubscribe', token: instrumentToken };
        socketRef.current.send(JSON.stringify(message));
        socketRef.current.close();
        console.log(`[Frontend WS] Disconnected and unsubscribed from ${instrumentToken}`);
      }
    };
  }, [instrumentToken]); // This effect re-runs if the user selects a new instrumentToken

  return (
    <div className="live-ticker-container">
      <div className="ticker-header">
        <h3>Live Price</h3>
        <div className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}>
          {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
        </div>
      </div>
      
      <div className="ticker-info">
        <div className="instrument-name">{instrumentName || 'Select an option'}</div>
        <div className={`live-price ${priceClass}`}>
          ₹{livePrice}
        </div>
      </div>
    </div>
  );
}

export default LiveTicker;
