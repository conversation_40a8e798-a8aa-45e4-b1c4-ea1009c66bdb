/* src/index.css */

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  background-color: #242424;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  width: 100%;
}

header {
  border-bottom: 1px solid #444;
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

h1 {
  font-size: 2.5em;
  line-height: 1.1;
  margin-bottom: 0.5rem;
}

.status-indicator {
  padding: 0.3rem 0.6rem;
  border-radius: 8px;
  display: inline-block;
  font-weight: bold;
}

.status-indicator.logged-in {
  background-color: #28a745;
  color: white;
}

.status-indicator.logged-out {
  background-color: #dc3545;
  color: white;
}

.login-form, .session-view {
  background-color: #2f2f2f;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #444;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

input {
  width: 100%;
  padding: 0.8rem;
  font-size: 1em;
  border-radius: 4px;
  border: 1px solid #555;
  background-color: #333;
  color: white;
  box-sizing: border-box;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.error-message {
  color: #ff6b6b;
  margin-top: 1rem;
  font-weight: bold;
}

.success-message {
  color: #28a745;
  font-weight: bold;
}

/* --- ADD THESE NEW STYLES FOR MARKET DATA --- */

/* Controls container */
.controls-container {
  display: flex;
  gap: 2rem;
  margin: 2rem 0;
  justify-content: center;
  flex-wrap: wrap;
}

/* Instrument and expiry selectors */
.instrument-selector,
.expiry-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.instrument-selector label,
.expiry-selector label {
  font-weight: bold;
  color: #ccc;
}

#instrument-select,
#expiry-select {
  padding: 0.5rem;
  font-size: 1em;
  border-radius: 4px;
  background-color: #333;
  color: white;
  border: 1px solid #555;
  min-width: 150px;
}

.price-display-wrapper {
  margin-bottom: 2rem;
}

.price-display {
  background-color: #1a1a1a;
  padding: 1.5rem 2rem;
  border-radius: 8px;
  font-size: 2.5em;
  font-weight: bold;
  letter-spacing: 1px;
}

.price-value {
  color: #87d0ff; /* A light blue color for the price */
}

/* --- Option Chain Table Styles --- */
.option-chain-container {
  margin-top: 2rem;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

th, td {
  padding: 0.75rem;
  border: 1px solid #444;
  text-align: center;
}

th {
  background-color: #3a3a3a;
  font-weight: bold;
  color: #fff;
}

.strike-price {
  font-weight: bold;
  background-color: #3f4e62;
  color: #fff;
}

.ce-price {
  color: #a6ffb8; /* Light green for calls */
  font-weight: bold;
}

.pe-price {
  color: #ff8a8a; /* Light red for puts */
  font-weight: bold;
}

.atm-strike {
  background-color: #4a4a2a;
  font-weight: bold;
}

.atm-strike .strike-price {
  background-color: #6a6a3a;
}

/* Clickable option cells */
.clickable-option {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-option:hover {
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  border: 1px solid #fff;
}

/* Live ticker styles */
.live-ticker-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #333;
}

.live-ticker-container {
  text-align: center;
}

.ticker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.ticker-header h3 {
  margin: 0;
  color: #fff;
}

.connection-status {
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.connection-status.connected {
  background-color: #1a4d1a;
  color: #90ee90;
}

.connection-status.disconnected {
  background-color: #4d1a1a;
  color: #ffb3b3;
}

.ticker-info {
  text-align: center;
}

.instrument-name {
  font-size: 1.1rem;
  color: #ccc;
  margin-bottom: 0.5rem;
}

.live-price {
  font-size: 2.5rem;
  font-weight: bold;
  color: #fff;
  transition: all 0.3s ease;
  padding: 0.5rem;
  border-radius: 8px;
  background-color: #2a2a2a;
}

.live-price.price-up {
  background-color: #1a4d1a;
  color: #90ee90;
  animation: priceFlash 0.7s ease-out;
}

.live-price.price-down {
  background-color: #4d1a1a;
  color: #ffb3b3;
  animation: priceFlash 0.7s ease-out;
}

@keyframes priceFlash {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
