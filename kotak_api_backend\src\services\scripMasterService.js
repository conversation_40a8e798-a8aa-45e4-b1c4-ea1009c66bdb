// src/services/scripMasterService.js

const axios = require('axios');
const { parse } = require('papaparse');

// This cache is now specifically for NSE F&O instruments (options/futures)
let fnoInstrumentCache = new Map();
let isInitialized = false;

const SCRIP_MASTER_FILE_PATHS_URL = 'https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths';

/**
 * Initializes the service by downloading and caching the NSE F&O scrip master.
 * This is essential for finding option tokens in Step 4.
 * @param {string} accessToken - A valid client access token.
 */
const initialize = async (accessToken) => {
  console.log('[ScripMaster] Initializing F&O data service...');

  try {
    // 1. Get the URL for the latest NSE FO (Futures & Options) file
    const filePathsResponse = await axios.get(SCRIP_MASTER_FILE_PATHS_URL, {
      headers: { 'Authorization': `Bear<PERSON> ${accessToken}` },
    });

    // ** THE CRITICAL CHANGE IS HERE: We look for nse_fo.csv, not nse_cm-v1.csv **
    const nseFoUrl = filePathsResponse.data.data.filesPaths.find(p => p.includes('nse_fo.csv'));

    if (!nseFoUrl) {
      throw new Error('NSE FO scrip master URL not found in API response.');
    }
    console.log(`[ScripMaster] Found NSE FO file URL: ${nseFoUrl}`);

    // 2. Download the F&O CSV file content
    const csvResponse = await axios.get(nseFoUrl);
    if (!csvResponse.data) {
      throw new Error('Downloaded NSE FO Scrip Master CSV file is empty.');
    }

    // 3. Parse and cache the F&O data for later use
    console.log('[ScripMaster] Parsing F&O CSV data...');
    const parsedData = parse(csvResponse.data, {
      header: true,
      skipEmptyLines: true,
    });

    fnoInstrumentCache.clear();
    // We will use this cache in Step 4 to find option tokens.
    // For now, we just confirm it's populated.
    parsedData.data.forEach(instrument => {
      // For options, we'll need a more complex key, but for now, token is enough
      fnoInstrumentCache.set(instrument.pToken.trim(), instrument);
    });

    isInitialized = true;
    console.log(`[ScripMaster] Initialization SUCCESS. Cached ${fnoInstrumentCache.size} F&O instruments.`);

  } catch (error) {
    console.error('[ScripMaster] FATAL ERROR during initialization:', error.message);
    isInitialized = false;
    // We must throw the error to let the login process know something went wrong.
    throw error;
  }
};

/**
 * Gets the full instrument details for a given F&O token.
 * We will use this function extensively in Step 4.
 * @param {string} token - The numeric instrument token for an option or future.
 * @returns {object|null} The instrument data object, or null if not found.
 */
const getFnoInstrumentByToken = (token) => {
  if (!isInitialized) {
    console.warn('[ScripMaster] F&O service not initialized.');
    return null;
  }
  return fnoInstrumentCache.get(token) || null;
};

module.exports = {
  initialize,
  getFnoInstrumentByToken,
};
