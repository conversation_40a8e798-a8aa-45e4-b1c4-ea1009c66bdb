// src/services/scripMasterService.js

const axios = require('axios');
const { parse } = require('papaparse');

// This cache will hold all Futures & Options instruments.
let fnoInstrumentCache = new Map();
let isInitialized = false;

const SCRIP_MASTER_FILE_PATHS_URL = 'https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths';

/**
 * Initializes the service by downloading, parsing, and caching the NSE F&O scrip master.
 * @param {string} accessToken - A valid client access token.
 */
const initializeFNOData = async (accessToken) => {
  console.log('[ScripMaster] Initializing F&O data service...');
  try {
    const filePathsResponse = await axios.get(SCRIP_MASTER_FILE_PATHS_URL, {
      headers: { 'Authorization': `Bearer ${accessToken}` },
    });

    const nseFoUrl = filePathsResponse.data.data.filesPaths.find(p => p.includes('nse_fo.csv'));
    if (!nseFoUrl) throw new Error('NSE FO scrip master URL not found.');

    console.log(`[ScripMaster] Downloading F&O file from: ${nseFoUrl}`);
    const csvResponse = await axios.get(nseFoUrl);
    if (!csvResponse.data) throw new Error('Downloaded F&O Scrip Master file is empty.');

    console.log('[ScripMaster] Parsing F&O CSV data...');
    const parsedData = parse(csvResponse.data, { header: true, skipEmptyLines: true });

    fnoInstrumentCache.clear();
    parsedData.data.forEach(instrument => {
      // Create a unique, predictable key for each option contract for easy lookup.
      // Format: SYMBOL-YYYYMMDD-STRIKE-OPTIONTYPE (e.g., NIFTY-20240725-25000-CE)
      if (instrument.pSymbol && instrument.pExpiryDate && instrument.dStrikePrice && instrument.pOptionType) {
        const expiryDate = new Date(parseInt(instrument.pExpiryDate) * 1000);
        const expiryString = expiryDate.toISOString().slice(0, 10).replace(/-/g, '');
        const strike = Math.round(parseFloat(instrument.dStrikePrice.replace(';', '')) / 100);
        const key = `${instrument.pSymbolName.trim()}-${expiryString}-${strike}-${instrument.pOptionType.trim()}`;
        fnoInstrumentCache.set(key.toUpperCase(), instrument.pSymbol.trim());
      }
    });

    isInitialized = true;
    console.log(`[ScripMaster] SUCCESS. Cached ${fnoInstrumentCache.size} F&O instruments.`);
  } catch (error) {
    console.error('[ScripMaster] FATAL ERROR during initialization:', error.message);
    isInitialized = false;
    throw error;
  }
};

/**
 * Gets the numeric token for a specific option contract from the cache.
 * @param {string} symbol - e.g., 'NIFTY'
 * @param {string} expiryDate - e.g., '20240725' (YYYYMMDD)
 * @param {number} strike - e.g., 25000
 * @param {string} optionType - 'CE' or 'PE'
 * @returns {string|null} The numeric token or null if not found.
 */
const getOptionToken = (symbol, expiryDate, strike, optionType) => {
  if (!isInitialized) return null;
  const key = `${symbol}-${expiryDate}-${strike}-${optionType}`.toUpperCase();
  return fnoInstrumentCache.get(key) || null;
};

module.exports = {
  initializeFNOData,
  getOptionToken,
};
