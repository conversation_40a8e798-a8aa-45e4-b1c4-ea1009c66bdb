// src/services/scripMasterService.js

const axios = require('axios');
const { parse } = require('papaparse');
const fs = require('fs');
const path = require('path');

// A simple in-memory cache for our scrip data to avoid re-processing the file
let instrumentCache = new Map();
let isInitialized = false;

const SCRIP_MASTER_FILE_PATHS_URL = 'https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths';

/**
 * Initializes the service by downloading, parsing, and caching scrip master data.
 * This should be called once when the server starts or on a daily schedule.
 * @param {string} accessToken - A valid client access token from Step 1 of auth.
 */
const initialize = async (accessToken) => {
  console.log('[ScripMaster] Initializing...');

  try {
    // 1. Get the URL for the latest NSE CM (Cash Market) Scrip Master file
    const filePathsResponse = await axios.get(SCRIP_MASTER_FILE_PATHS_URL, {
      headers: { 'Authorization': `Bear<PERSON> ${accessToken}` }
    });
    
    // Find the specific URL for the nse_cm-v1.csv file
    const nseCmUrl = filePathsResponse.data.data.filesPaths.find(p => p.includes('nse_cm-v1.csv'));
    if (!nseCmUrl) {
      throw new Error('NSE CM v1 scrip master URL not found in API response.');
    }
    console.log(`[ScripMaster] Found NSE CM file URL: ${nseCmUrl}`);

    // 2. Download the CSV file content
    const csvResponse = await axios.get(nseCmUrl);
    if (!csvResponse.data) {
      throw new Error('Downloaded Scrip Master CSV file is empty.');
    }

    // 3. Parse the CSV data and cache it
    console.log('[ScripMaster] Parsing CSV data...');
    const parsedData = parse(csvResponse.data, {
      header: true, // Treat the first row as headers
      skipEmptyLines: true,
    });
    
    // Clear the old cache and populate it with new data
    instrumentCache.clear();
    parsedData.data.forEach(instrument => {
      // Create a unique key for each instrument, e.g., 'nse_cm|RELIANCE'
      const key = `nse_cm|${instrument.pSymbol.trim()}`;
      // Store the essential data: the numeric token
      instrumentCache.set(key, instrument.pToken.trim());
    });

    isInitialized = true;
    console.log(`[ScripMaster] Initialization SUCCESS. Cached ${instrumentCache.size} instruments.`);
    
    // For debugging: Let's check if our indices were found and what their tokens are.
    console.log(`[ScripMaster] DEBUG: NIFTY 50 token -> ${instrumentCache.get('nse_cm|NIFTY 50')}`);
    console.log(`[ScripMaster] DEBUG: Nifty Bank token -> ${instrumentCache.get('nse_cm|NIFTY BANK')}`);

  } catch (error) {
    console.error('[ScripMaster] FATAL ERROR during initialization:', error.message);
    isInitialized = false;
    // In a production app, you might want to retry or shut down gracefully.
  }
};

/**
 * Gets the numeric token for a given instrument symbol.
 * @param {string} instrumentName - e.g., "NIFTY 50" or "NIFTY BANK"
 * @returns {string|null} The numeric token, or null if not found.
 */
const getTokenFor = (instrumentName) => {
  if (!isInitialized) {
    console.warn('[ScripMaster] Service not initialized. Cannot get token.');
    return null;
  }
  // Construct the key and look it up in our cache. Note the potential space difference.
  const token = instrumentCache.get(`nse_cm|${instrumentName.toUpperCase()}`);
  return token || null;
};

module.exports = {
  initialize,
  getTokenFor,
};
