// src/services/scripMasterService.js

const axios = require('axios');
const { parse } = require('papaparse');

// This cache will hold all Futures & Options instruments.
let fnoInstrumentCache = new Map();
let isInitialized = false;

const SCRIP_MASTER_FILE_PATHS_URL = 'https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths';

/**
 * Initializes the service by downloading, parsing, and caching the NSE F&O scrip master.
 * @param {string} accessToken - A valid client access token.
 */
const initializeFNOData = async (accessToken) => {
  console.log('[ScripMaster] Initializing F&O data service...');
  try {
    const filePathsResponse = await axios.get(SCRIP_MASTER_FILE_PATHS_URL, {
      headers: { 'Authorization': `Bearer ${accessToken}` },
    });

    const nseFoUrl = filePathsResponse.data.data.filesPaths.find(p => p.includes('nse_fo.csv'));
    if (!nseFoUrl) throw new Error('NSE FO scrip master URL not found.');

    console.log(`[ScripMaster] Downloading F&O file from: ${nseFoUrl}`);
    const csvResponse = await axios.get(nseFoUrl);
    if (!csvResponse.data) throw new Error('Downloaded F&O Scrip Master file is empty.');

    console.log('[ScripMaster] Parsing F&O CSV data...');
    const parsedData = parse(csvResponse.data, { header: true, skipEmptyLines: true });

    // CSV structure confirmed: dStrikePrice; = strike price, pInstName = OPTSTK for options

    fnoInstrumentCache.clear();

    parsedData.data.forEach(instrument => {
      // --- DEFINITIVE FIX: Filter for INDEX OPTIONS ---
      if (instrument.pInstName && instrument.pInstName.trim() === 'OPTIDX') {
        const symbol = instrument.pSymbolName.trim().toUpperCase(); // NIFTY or BANKNIFTY

        // --- DEFINITIVE FIX: Correctly handle the Strike Price column name and value ---
        const strikePriceStr = instrument['dStrikePrice;'] || instrument['dStrikePrice'];
        const strike = parseFloat(strikePriceStr) / 100;

        // --- DEFINITIVE FIX: Correctly handle the Expiry Date conversion ---
        const epoch = parseInt(instrument.pExpiryDate, 10);
        // Per Kotak docs, add this offset for nse_fo dates
        const correctedEpoch = epoch + *********;
        const expiry = new Date(correctedEpoch * 1000);
        const expiryString = expiry.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD

        const optionType = instrument.pOptionType.trim().toUpperCase();
        const token = instrument.pSymbol.trim(); // The token is in the 'pSymbol' column

        // The key must use the corrected, calculated values
        const key = `${symbol}-${expiryString}-${strike}-${optionType}`;
        fnoInstrumentCache.set(key, token);
      }
    });

    isInitialized = true;
    console.log(`[ScripMaster] SUCCESS. Cached ${fnoInstrumentCache.size} F&O instruments.`);
    // Example debug to confirm a key exists after parsing
    const sampleKey = Array.from(fnoInstrumentCache.keys())[0];
    console.log(`[ScripMaster] DEBUG: Sample cached key -> ${sampleKey}`);
  } catch (error) {
    console.error('[ScripMaster] FATAL ERROR during initialization:', error.message);
    isInitialized = false;
    throw error;
  }
};

/**
 * Gets the numeric token for a specific option contract from the cache.
 * @param {string} symbol - e.g., 'NIFTY'
 * @param {string} expiryDate - e.g., '20240725' (YYYYMMDD)
 * @param {number} strike - e.g., 25000
 * @param {string} optionType - 'CE' or 'PE'
 * @returns {string|null} The numeric token or null if not found.
 */
const getOptionToken = (symbol, expiryDate, strike, optionType) => {
  if (!isInitialized) return null;
  const key = `${symbol.toUpperCase()}-${expiryDate}-${strike}-${optionType.toUpperCase()}`;
  const token = fnoInstrumentCache.get(key);
  if(!token) {
      console.warn(`[ScripMaster] Token not found for key: ${key}`);
  }
  return token || null;
};

module.exports = {
  initializeFNOData,
  getOptionToken,
};
