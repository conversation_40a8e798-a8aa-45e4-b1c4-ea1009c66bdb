// src/services/scripMasterService.js

const axios = require('axios');
const { parse } = require('papaparse');

// This cache will hold all Futures & Options instruments.
let fnoInstrumentCache = new Map();
// This new cache will store available expiry dates for each underlying
let expiryDatesCache = new Map();
let isInitialized = false;

const SCRIP_MASTER_FILE_PATHS_URL = 'https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths';

/**
 * Initializes the service by downloading, parsing, and caching the NSE F&O scrip master.
 * @param {string} accessToken - A valid client access token.
 */
const initializeFNOData = async (accessToken) => {
  console.log('[ScripMaster] Initializing F&O data service...');
  try {
    const filePathsResponse = await axios.get(SCRIP_MASTER_FILE_PATHS_URL, {
      headers: { 'Authorization': `Bearer ${accessToken}` },
    });

    const nseFoUrl = filePathsResponse.data.data.filesPaths.find(p => p.includes('nse_fo.csv'));
    if (!nseFoUrl) throw new Error('NSE FO scrip master URL not found.');

    console.log(`[ScripMaster] Downloading F&O file from: ${nseFoUrl}`);
    const csvResponse = await axios.get(nseFoUrl);
    if (!csvResponse.data) throw new Error('Downloaded F&O Scrip Master file is empty.');

    console.log('[ScripMaster] Parsing F&O CSV data...');
    const parsedData = parse(csvResponse.data, { header: true, skipEmptyLines: true });

    // CSV structure confirmed: dStrikePrice; = strike price, pInstName = OPTSTK for options

    fnoInstrumentCache.clear();
    expiryDatesCache.clear();

    const tempExpiryDates = {};

    parsedData.data.forEach((instrument, index) => {
      try {
        // We only care about index options (NIFTY and BANKNIFTY)
        const symbolName = instrument.pSymbolName ? instrument.pSymbolName.trim().toUpperCase() : '';

        if ((symbolName === 'NIFTY' || symbolName === 'BANKNIFTY') &&
            instrument.pInstName && instrument.pInstName.trim() === 'OPTSTK') {

          const expiryTimestamp = parseInt(instrument.pExpiryDate);
          const expiryDate = new Date(expiryTimestamp * 1000);
          const expiryString = expiryDate.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD

          // Handle strike price more safely
          let strikePrice = instrument['dStrikePrice;'] || instrument.dStrikePrice || '0';
          if (typeof strikePrice === 'string') {
            strikePrice = strikePrice.replace(';', '');
          }
          const strike = Math.round(parseFloat(strikePrice) / 100);

          const optionType = instrument.pOptionType.trim();
          const token = instrument.pSymbol.trim();

          // Populate the main token cache
          const key = `${symbolName}-${expiryString}-${strike}-${optionType}`;
          fnoInstrumentCache.set(key, token);

          // Populate the temporary expiry date collection
          if (!tempExpiryDates[symbolName]) {
            tempExpiryDates[symbolName] = new Set();
          }
          tempExpiryDates[symbolName].add(expiryString);
        }
      } catch (error) {
        console.warn(`[ScripMaster] Warning: Error processing row ${index}:`, error.message);
      }
    });

    // Process the collected expiry dates into a sorted list for easy access
    for (const symbol in tempExpiryDates) {
      const sortedDates = Array.from(tempExpiryDates[symbol]).sort();
      expiryDatesCache.set(symbol, sortedDates);
    }

    isInitialized = true;
    console.log(`[ScripMaster] SUCCESS. Cached ${fnoInstrumentCache.size} F&O instruments.`);
    console.log(`[ScripMaster] DEBUG: NIFTY expiries found ->`, expiryDatesCache.get('NIFTY'));
    console.log(`[ScripMaster] DEBUG: BANKNIFTY expiries found ->`, expiryDatesCache.get('BANKNIFTY'));
  } catch (error) {
    console.error('[ScripMaster] FATAL ERROR during initialization:', error.message);
    isInitialized = false;
    throw error;
  }
};

/**
 * Gets the nearest upcoming weekly expiry date for a given symbol from the file.
 * @param {string} symbol - 'NIFTY' or 'BANKNIFTY'
 * @returns {string|null} The nearest expiry date in YYYYMMDD format, or null.
 */
const getNearestExpiry = (symbol) => {
  if (!isInitialized) return null;
  const dates = expiryDatesCache.get(symbol.toUpperCase());
  if (!dates || dates.length === 0) return null;

  // Filter for future dates and return the nearest one
  const currentDateString = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const futureDates = dates.filter(date => date >= currentDateString);

  return futureDates.length > 0 ? futureDates[0] : dates[0]; // Fallback to first date if no future dates
};

/**
 * Gets the numeric token for a specific option contract from the cache.
 * @param {string} symbol - e.g., 'NIFTY'
 * @param {string} expiryDate - e.g., '********' (YYYYMMDD)
 * @param {number} strike - e.g., 25000
 * @param {string} optionType - 'CE' or 'PE'
 * @returns {string|null} The numeric token or null if not found.
 */
const getOptionToken = (symbol, expiryDate, strike, optionType) => {
  if (!isInitialized) return null;
  const key = `${symbol.toUpperCase()}-${expiryDate}-${strike}-${optionType.toUpperCase()}`;
  return fnoInstrumentCache.get(key) || null;
};

module.exports = {
  initializeFNOData,
  getOptionToken,
  getNearestExpiry, // <-- Exporting our new, intelligent function
};
