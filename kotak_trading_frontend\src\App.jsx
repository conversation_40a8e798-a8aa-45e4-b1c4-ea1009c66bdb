// src/App.jsx

import React, { useState, useEffect } from 'react';

// The URL of the backend server we built in Step 1
const API_BASE_URL = 'http://localhost:3001';

function App() {
  // --- State Management ---
  // Holds the value of the TOTP input field
  const [totp, setTotp] = useState('');
  // Tracks if the user is currently authenticated
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  // Shows a loading spinner/message during API calls
  const [isLoading, setIsLoading] = useState(true);
  // Stores any error messages from the backend
  const [error, setError] = useState('');

  // --- Side Effects ---
  // This `useEffect` runs once when the component first loads
  useEffect(() => {
    // Check the server for an existing session
    const checkAuthStatus = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/auth/status`);
        const data = await response.json();
        setIsLoggedIn(data.isLoggedIn);
      } catch (err) {
        setError('Cannot connect to the backend server. Is it running?');
      } finally {
        setIsLoading(false);
      }
    };
    checkAuthStatus();
  }, []); // The empty array [] means this effect runs only once

  // --- Event Handlers ---
  const handleLogin = async (event) => {
    event.preventDefault(); // Prevent the form from reloading the page
    setIsLoading(true);
    setError(''); // Clear previous errors

    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ totp }),
      });

      const data = await response.json();

      if (response.ok) { // Status code 200-299
        setIsLoggedIn(true);
      } else {
        setError(data.message || 'An unknown error occurred.');
      }
    } catch (err) {
      setError('Network error: Could not reach the server.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    await fetch(`${API_BASE_URL}/api/auth/logout`, { method: 'POST' });
    setIsLoggedIn(false);
    setTotp('');
    setError('');
    setIsLoading(false);
  };

  // --- UI Rendering ---
  if (isLoading) {
    return <div className="container"><h1>Loading...</h1></div>;
  }

  return (
    <div className="container">
      <header>
        <h1>Kotak Neo Trading Panel</h1>
        <div className={`status-indicator ${isLoggedIn ? 'logged-in' : 'logged-out'}`}>
          {isLoggedIn ? 'Status: Connected' : 'Status: Disconnected'}
        </div>
      </header>

      <main>
        {isLoggedIn ? (
          <div className="session-view">
            <h2>Welcome!</h2>
            <p className="success-message">You are successfully authenticated.</p>
            <p>We are now ready to fetch market data.</p>
            <button onClick={handleLogout} disabled={isLoading}>
              Logout
            </button>
          </div>
        ) : (
          <form onSubmit={handleLogin} className="login-form">
            <h2>Login Required</h2>
            <p>Please enter your TOTP from your authenticator app.</p>
            <div className="form-group">
              <label htmlFor="totp">One-Time Password (TOTP)</label>
              <input
                id="totp"
                type="text"
                value={totp}
                onChange={(e) => setTotp(e.target.value)}
                placeholder="e.g., 123456"
                required
              />
            </div>
            <button type="submit" disabled={isLoading}>
              {isLoading ? 'Logging in...' : 'Login'}
            </button>
            {error && <p className="error-message">{error}</p>}
          </form>
        )}
      </main>
    </div>
  );
}

export default App;
