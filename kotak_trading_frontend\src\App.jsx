// src/App.jsx

import React, { useState, useEffect, useRef } from 'react';

const API_BASE_URL = 'http://localhost:3001';

// --- New Component for the Main Trading View ---
function TradingView({ onLogout }) {
  // State for the data dashboard
  const [selectedInstrument, setSelectedInstrument] = useState('NIFTY_50');
  const [indexPrice, setIndexPrice] = useState(null);
  const [dataIsLoading, setDataIsLoading] = useState(true);
  const [dataError, setDataError] = useState('');

  // useRef is used to hold the interval ID without causing re-renders
  const intervalRef = useRef(null);

  // This useEffect hook is the core of our data fetching logic
  useEffect(() => {
    // Function to fetch the price from our backend
    const fetchPrice = async () => {
      setDataIsLoading(true);
      setDataError('');
      try {
        const response = await fetch(`${API_BASE_URL}/api/data/index-ltp?instrument=${selectedInstrument}`);
        const data = await response.json();

        if (response.ok) {
          setIndexPrice(data.ltp);
        } else {
          // Handle errors from our backend, like a 401 Unauthorized
          setDataError(data.message || 'Failed to fetch data.');
          setIndexPrice(null); // Clear old price on error
        }
      } catch (err) {
        setDataError('Network error. Cannot reach backend.');
        setIndexPrice(null);
      } finally {
        setDataIsLoading(false);
      }
    };

    // 1. Fetch price immediately when the component loads or selection changes
    fetchPrice();

    // 2. Set up an interval to refetch the price every 5 seconds
    intervalRef.current = setInterval(fetchPrice, 5000);

    // 3. IMPORTANT: Cleanup function to clear the interval
    // This runs when the component unmounts (on logout) or when selectedInstrument changes.
    // It prevents memory leaks and old intervals from running.
    return () => {
      clearInterval(intervalRef.current);
    };
  }, [selectedInstrument]); // The effect re-runs whenever selectedInstrument changes

  return (
    <div className="session-view">
      <h2>Market Data</h2>

      <div className="instrument-selector">
        <label htmlFor="instrument-select">Select Index:</label>
        <select
          id="instrument-select"
          value={selectedInstrument}
          onChange={(e) => setSelectedInstrument(e.target.value)}
        >
          <option value="NIFTY_50">Nifty 50</option>
          <option value="BANK_NIFTY">Bank Nifty</option>
        </select>
      </div>

      <div className="price-display-wrapper">
        <div className="price-display">
          {dataError ? (
            <span className="error-message">{dataError}</span>
          ) : dataIsLoading ? (
            <span>Loading Price...</span>
          ) : (
            <span className="price-value">
              {indexPrice !== null ? indexPrice.toFixed(2) : 'N/A'}
            </span>
          )}
        </div>
      </div>

      <button onClick={onLogout}>Logout</button>
    </div>
  );
}


// --- Main App Component (Mostly unchanged) ---
function App() {
  const [totp, setTotp] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/auth/status`);
        const data = await response.json();
        setIsLoggedIn(data.isLoggedIn);
      } catch (err) {
        setError('Cannot connect to the backend server. Is it running?');
      } finally {
        setIsLoading(false);
      }
    };
    checkAuthStatus();
  }, []);

  const handleLogin = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ totp }),
      });
      const data = await response.json();
      if (response.ok) {
        setIsLoggedIn(true);
      } else {
        setError(data.message || 'An unknown error occurred.');
      }
    } catch (err) {
      setError('Network error: Could not reach the server.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    await fetch(`${API_BASE_URL}/api/auth/logout`, { method: 'POST' });
    setIsLoggedIn(false);
    setTotp('');
    setError('');
    setIsLoading(false);
  };

  if (isLoading) {
    return <div className="container"><h1>Loading...</h1></div>;
  }

  return (
    <div className="container">
      <header>
        <h1>Kotak Neo Trading Panel</h1>
        <div className={`status-indicator ${isLoggedIn ? 'logged-in' : 'logged-out'}`}>
          {isLoggedIn ? 'Status: Connected' : 'Status: Disconnected'}
        </div>
      </header>

      <main>
        {isLoggedIn ? (
          <TradingView onLogout={handleLogout} />
        ) : (
          <form onSubmit={handleLogin} className="login-form">
            <h2>Login Required</h2>
            <p>Please enter your TOTP from your authenticator app.</p>
            <div className="form-group">
              <label htmlFor="totp">One-Time Password (TOTP)</label>
              <input
                id="totp"
                type="text"
                value={totp}
                onChange={(e) => setTotp(e.target.value)}
                placeholder="e.g., 123456"
                required
              />
            </div>
            <button type="submit" disabled={isLoading}>
              {isLoading ? 'Logging in...' : 'Login'}
            </button>
            {error && <p className="error-message">{error}</p>}
          </form>
        )}
      </main>
    </div>
  );
}

export default App;
