// src/App.jsx

import React, { useState, useEffect, useRef } from 'react';

const API_BASE_URL = 'http://localhost:3001';

// --- New Component for the Main Trading View ---
function TradingView({ onLogout }) {
  // State for the data dashboard
  const [selectedInstrument, setSelectedInstrument] = useState('NIFTY_50');
  const [indexPrice, setIndexPrice] = useState(null);
  const [optionChain, setOptionChain] = useState([]); // <-- New state for option chain data
  const [dataIsLoading, setDataIsLoading] = useState(true);
  const [dataError, setDataError] = useState('');

  // useRef is used to hold the interval ID without causing re-renders
  const intervalRef = useRef(null);

  // This useEffect hook is the core of our data fetching logic
  useEffect(() => {
    // This function will now fetch both index price and the option chain
    const fetchData = async () => {
      setDataIsLoading(true);
      setDataError('');
      try {
        // --- Step 1: Fetch the live index price (from Yahoo) ---
        const indexResponse = await fetch(`${API_BASE_URL}/api/data/index-ltp?instrument=${selectedInstrument}`);
        const indexData = await indexResponse.json();

        if (!indexResponse.ok) throw new Error(indexData.message || 'Failed to fetch index price');

        const currentPrice = indexData.ltp;
        setIndexPrice(currentPrice);

        // --- Step 2: Fetch the option chain using the live index price (from Kotak) ---
        const ocResponse = await fetch(`${API_BASE_URL}/api/data/option-chain?instrument=${selectedInstrument}&indexPrice=${currentPrice}`);
        const ocData = await ocResponse.json();

        if (!ocResponse.ok) throw new Error(ocData.message || 'Failed to fetch option chain');

        setOptionChain(ocData.optionChain);

      } catch (err) {
        setDataError(err.message);
        setOptionChain([]); // Clear old data on error
      } finally {
        setDataIsLoading(false);
      }
    };

    // 1. Fetch data immediately when the component loads or selection changes
    fetchData();

    // 2. Set up an interval to refetch the data every 5 seconds
    intervalRef.current = setInterval(fetchData, 5000);

    // 3. IMPORTANT: Cleanup function to clear the interval
    // This runs when the component unmounts (on logout) or when selectedInstrument changes.
    // It prevents memory leaks and old intervals from running.
    return () => {
      clearInterval(intervalRef.current);
    };
  }, [selectedInstrument]); // The effect re-runs whenever selectedInstrument changes

  return (
    <div className="session-view">
      <h2>Market Data</h2>

      <div className="instrument-selector">
        <label htmlFor="instrument-select">Select Index:</label>
        <select
          id="instrument-select"
          value={selectedInstrument}
          onChange={(e) => setSelectedInstrument(e.target.value)}
        >
          <option value="NIFTY_50">Nifty 50</option>
          <option value="BANK_NIFTY">Bank Nifty</option>
        </select>
      </div>

      <div className="price-display-wrapper">
        <div className="price-display">
          {dataError ? (
            <span className="error-message">{dataError}</span>
          ) : dataIsLoading ? (
            <span>Loading Price...</span>
          ) : (
            <span className="price-value">
              {indexPrice !== null ? indexPrice.toFixed(2) : 'N/A'}
            </span>
          )}
        </div>
      </div>

      {/* --- New Option Chain Table --- */}
      <div className="option-chain-container">
        {dataIsLoading && !optionChain.length ? (
          <p>Loading Option Chain...</p>
        ) : dataError ? (
          <p className="error-message">{dataError}</p>
        ) : (
          <table>
            <thead>
              <tr>
                <th>CALL LTP</th>
                <th>STRIKE</th>
                <th>PUT LTP</th>
              </tr>
            </thead>
            <tbody>
              {optionChain.map(({ strike, ce_ltp, pe_ltp }) => (
                <tr key={strike} className={Math.abs(strike - indexPrice) < 50 ? 'atm-strike' : ''}>
                  <td className="ce-price">{ce_ltp}</td>
                  <td className="strike-price">{strike}</td>
                  <td className="pe-price">{pe_ltp}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      <button onClick={onLogout}>Logout</button>
    </div>
  );
}


// --- Main App Component (Mostly unchanged) ---
function App() {
  const [totp, setTotp] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/auth/status`);
        const data = await response.json();
        setIsLoggedIn(data.isLoggedIn);
      } catch (err) {
        setError('Cannot connect to the backend server. Is it running?');
      } finally {
        setIsLoading(false);
      }
    };
    checkAuthStatus();
  }, []);

  const handleLogin = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ totp }),
      });
      const data = await response.json();
      if (response.ok) {
        setIsLoggedIn(true);
      } else {
        setError(data.message || 'An unknown error occurred.');
      }
    } catch (err) {
      setError('Network error: Could not reach the server.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    await fetch(`${API_BASE_URL}/api/auth/logout`, { method: 'POST' });
    setIsLoggedIn(false);
    setTotp('');
    setError('');
    setIsLoading(false);
  };

  if (isLoading) {
    return <div className="container"><h1>Loading...</h1></div>;
  }

  return (
    <div className="container">
      <header>
        <h1>Kotak Neo Trading Panel</h1>
        <div className={`status-indicator ${isLoggedIn ? 'logged-in' : 'logged-out'}`}>
          {isLoggedIn ? 'Status: Connected' : 'Status: Disconnected'}
        </div>
      </header>

      <main>
        {isLoggedIn ? (
          <TradingView onLogout={handleLogout} />
        ) : (
          <form onSubmit={handleLogin} className="login-form">
            <h2>Login Required</h2>
            <p>Please enter your TOTP from your authenticator app.</p>
            <div className="form-group">
              <label htmlFor="totp">One-Time Password (TOTP)</label>
              <input
                id="totp"
                type="text"
                value={totp}
                onChange={(e) => setTotp(e.target.value)}
                placeholder="e.g., 123456"
                required
              />
            </div>
            <button type="submit" disabled={isLoading}>
              {isLoading ? 'Logging in...' : 'Login'}
            </button>
            {error && <p className="error-message">{error}</p>}
          </form>
        )}
      </main>
    </div>
  );
}

export default App;
