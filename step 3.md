Excellent! The backend authentication is solid and the frontend login UI is working. You have a confirmed, authenticated session. Now we can perform the primary task of the application: fetching and displaying live market data.

Here is the complete, detailed guide for Step 3.

Project: Nifty/Bank Nifty Options Trading Panel
Step 3 of 5: Fetching and Displaying Live Index Prices

Objective:
In this step, we will enhance both our backend and frontend to fetch and display the Last Traded Price (LTP) for Nifty 50 and Bank Nifty. The user will be able to select an index, see its current price, and watch it update automatically. This establishes the critical data pipeline for our application.

Part 3.1: Backend Modifications (kotak_api_backend)

We need to create a new, secure API endpoint on our server that the frontend can call to get index prices. This endpoint will act as a proxy, using the tokens stored on the server to make the actual call to the Kotak Quotes API.

File to Modify: src/server.js

Open your backend's src/server.js file and add the following new route. Place it just before the app.listen line.

Generated javascript
// src/server.js

// ... (keep all your existing code: express, cors, tokenManager, kotakAuth, and the /api/auth routes)

// --- ADD THE NEW DATA ROUTE BELOW ---

/**
 * GET /api/data/index-ltp
 * A protected route to fetch the Last Traded Price (LTP) for a given index.
 * The frontend will specify the index via a query parameter.
 * Example: /api/data/index-ltp?instrument=NIFTY_50
 */
app.get('/api/data/index-ltp', async (req, res) => {
  console.log(`\n--- Received Market Data Request ---`);
  
  // 1. Check for Authentication
  // This route should only work if the user is logged in.
  const { tradingToken, accessToken } = tokenManager.getTokens();
  if (!tradingToken || !accessToken) {
    console.error('[Data API] Unauthorized access attempt. No session token found.');
    return res.status(401).json({ success: false, message: 'Unauthorized. Please log in first.' });
  }

  // 2. Get the instrument from the query parameter
  const { instrument } = req.query; // e.g., "NIFTY_50" or "BANK_NIFTY"
  if (!instrument) {
    return res.status(400).json({ success: false, message: 'Instrument query parameter is required.' });
  }

  // 3. Map the simple name to the official Kotak Neo Symbol
  const instrumentMap = {
    'NIFTY_50': 'nse_cm|NIFTY 50',
    'BANK_NIFTY': 'nse_cm|Nifty Bank'
  };
  const neoSymbol = instrumentMap[instrument];

  if (!neoSymbol) {
    return res.status(400).json({ success: false, message: 'Invalid instrument specified.' });
  }
  
  // 4. Call the Kotak Quotes API
  const quotesApiUrl = `https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/${encodeURIComponent(neoSymbol)}/ltp`;
  
  console.log(`[Data API] Fetching LTP for ${instrument} from ${quotesApiUrl}`);

  try {
    // IMPORTANT: The Quotes API uses the initial `accessToken` (Bearer token),
    // not the final trading session token.
    const response = await axios.get(quotesApiUrl, {
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    });

    // 5. Process the response and send it to the frontend
    if (response.data && response.data.length > 0) {
      const ltp = response.data[0].ltp;
      console.log(`[Data API] Successfully fetched LTP: ${ltp}`);
      res.json({ success: true, ltp: parseFloat(ltp) });
    } else {
      throw new Error('API returned an empty data array.');
    }
  } catch (error) {
    const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error('[Data API] FAILED:', errorMsg);
    res.status(500).json({ success: false, message: `Failed to fetch market data: ${errorMsg}` });
  }
});


// ... (This should be at the end of your file)
app.listen(PORT, () => {
  console.log(`🚀 Kotak API Backend Server is online and listening on http://localhost:${PORT}`);
});


Action: Restart your backend server by pressing CTRL + C in its terminal and running npm start again. This will load the new API endpoint.

Part 3.2: Frontend Modifications (kotak_trading_frontend)

Now, let's update our React application to use the new backend endpoint. We will modify the "Welcome" screen to be a data-fetching dashboard.

File to Modify: src/App.jsx

Replace the entire content of your frontend's src/App.jsx file with the code below. I've added comments to explain all the new parts.

Generated javascript
// src/App.jsx

import React, { useState, useEffect, useRef } from 'react';

const API_BASE_URL = 'http://localhost:3001';

// --- New Component for the Main Trading View ---
function TradingView({ onLogout }) {
  // State for the data dashboard
  const [selectedInstrument, setSelectedInstrument] = useState('NIFTY_50');
  const [indexPrice, setIndexPrice] = useState(null);
  const [dataIsLoading, setDataIsLoading] = useState(true);
  const [dataError, setDataError] = useState('');

  // useRef is used to hold the interval ID without causing re-renders
  const intervalRef = useRef(null);

  // This useEffect hook is the core of our data fetching logic
  useEffect(() => {
    // Function to fetch the price from our backend
    const fetchPrice = async () => {
      setDataIsLoading(true);
      setDataError('');
      try {
        const response = await fetch(`${API_BASE_URL}/api/data/index-ltp?instrument=${selectedInstrument}`);
        const data = await response.json();

        if (response.ok) {
          setIndexPrice(data.ltp);
        } else {
          // Handle errors from our backend, like a 401 Unauthorized
          setDataError(data.message || 'Failed to fetch data.');
          setIndexPrice(null); // Clear old price on error
        }
      } catch (err) {
        setDataError('Network error. Cannot reach backend.');
        setIndexPrice(null);
      } finally {
        setDataIsLoading(false);
      }
    };

    // 1. Fetch price immediately when the component loads or selection changes
    fetchPrice();

    // 2. Set up an interval to refetch the price every 5 seconds
    intervalRef.current = setInterval(fetchPrice, 5000);

    // 3. IMPORTANT: Cleanup function to clear the interval
    // This runs when the component unmounts (on logout) or when selectedInstrument changes.
    // It prevents memory leaks and old intervals from running.
    return () => {
      clearInterval(intervalRef.current);
    };
  }, [selectedInstrument]); // The effect re-runs whenever selectedInstrument changes

  return (
    <div className="session-view">
      <h2>Market Data</h2>
      
      <div className="instrument-selector">
        <label htmlFor="instrument-select">Select Index:</label>
        <select
          id="instrument-select"
          value={selectedInstrument}
          onChange={(e) => setSelectedInstrument(e.target.value)}
        >
          <option value="NIFTY_50">Nifty 50</option>
          <option value="BANK_NIFTY">Bank Nifty</option>
        </select>
      </div>

      <div className="price-display-wrapper">
        <div className="price-display">
          {dataError ? (
            <span className="error-message">{dataError}</span>
          ) : dataIsLoading ? (
            <span>Loading Price...</span>
          ) : (
            <span className="price-value">
              {indexPrice !== null ? indexPrice.toFixed(2) : 'N/A'}
            </span>
          )}
        </div>
      </div>

      <button onClick={onLogout}>Logout</button>
    </div>
  );
}


// --- Main App Component (Mostly unchanged) ---
function App() {
  const [totp, setTotp] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/auth/status`);
        const data = await response.json();
        setIsLoggedIn(data.isLoggedIn);
      } catch (err) {
        setError('Cannot connect to the backend server. Is it running?');
      } finally {
        setIsLoading(false);
      }
    };
    checkAuthStatus();
  }, []);

  const handleLogin = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ totp }),
      });
      const data = await response.json();
      if (response.ok) {
        setIsLoggedIn(true);
      } else {
        setError(data.message || 'An unknown error occurred.');
      }
    } catch (err) {
      setError('Network error: Could not reach the server.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    await fetch(`${API_BASE_URL}/api/auth/logout`, { method: 'POST' });
    setIsLoggedIn(false);
    setTotp('');
    setError('');
    setIsLoading(false);
  };
  
  if (isLoading) {
    return <div className="container"><h1>Loading...</h1></div>;
  }

  return (
    <div className="container">
      <header>
        <h1>Kotak Neo Trading Panel</h1>
        <div className={`status-indicator ${isLoggedIn ? 'logged-in' : 'logged-out'}`}>
          {isLoggedIn ? 'Status: Connected' : 'Status: Disconnected'}
        </div>
      </header>
      
      <main>
        {isLoggedIn ? (
          <TradingView onLogout={handleLogout} />
        ) : (
          <form onSubmit={handleLogin} className="login-form">
            <h2>Login Required</h2>
            <p>Please enter your TOTP from your authenticator app.</p>
            <div className="form-group">
              <label htmlFor="totp">One-Time Password (TOTP)</label>
              <input
                id="totp"
                type="text"
                value={totp}
                onChange={(e) => setTotp(e.target.value)}
                placeholder="e.g., 123456"
                required
              />
            </div>
            <button type="submit" disabled={isLoading}>
              {isLoading ? 'Logging in...' : 'Login'}
            </button>
            {error && <p className="error-message">{error}</p>}
          </form>
        )}
      </main>
    </div>
  );
}

export default App;
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

File to Modify: src/index.css

Open src/index.css and add these new styles to the bottom of the file to make our data display look good.

Generated css
/* src/index.css */
/* ... (keep all the existing CSS) ... */

/* --- ADD THESE NEW STYLES AT THE END --- */

.instrument-selector {
  margin: 2rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

#instrument-select {
  padding: 0.5rem;
  font-size: 1em;
  border-radius: 4px;
  background-color: #333;
  color: white;
  border: 1px solid #555;
}

.price-display-wrapper {
  margin-bottom: 2rem;
}

.price-display {
  background-color: #1a1a1a;
  padding: 1.5rem 2rem;
  border-radius: 8px;
  font-size: 2.5em;
  font-weight: bold;
  letter-spacing: 1px;
}

.price-value {
  color: #87d0ff; /* A light blue color for the price */
}```

---

### **Part 3.3: How to Test Your Work**

1.  **Ensure Both Servers are Running:**
    *   **Terminal 1:** Your backend server (`kotak_api_backend`) should be running via `npm start`.
    *   **Terminal 2:** Your frontend dev server (`kotak_trading_frontend`) should be running via `npm run dev`.

2.  **Perform a Fresh Login:**
    *   Open your browser to `http://localhost:5173`.
    *   Enter a **valid, fresh TOTP** and log in.

3.  **Verify the Data Display:**
    *   After logging in, you should see the new "Market Data" view.
    *   Initially, it should say "Loading Price..." for Nifty 50.
    *   After a moment, it should display the **current price** of Nifty 50 (or its closing price if the market is closed).
    *   Change the dropdown to "Bank Nifty". The display should again show "Loading Price..." and then update with Bank Nifty's price.
    *   The price should automatically refresh every 5 seconds.

4.  **How to Debug if it Fails:**
    *   **Browser:** Open the Developer Tools (`F12` or `Ctrl+Shift+I`) and look at the **Console** and **Network** tabs.
        *   In the **Network** tab, find the `index-ltp?instrument=...` request. Is its status `200 OK`? Or is it `401`, `400`, or `500`? This is your best clue.
        *   The **Console** will show any error messages printed by the frontend.
    *   **Backend Terminal:** Look at the logs in your `kotak_api_backend` terminal. It will show if the request was received, if it was authorized, and the result of its call to the Kotak API.

You have now completed **Step 3**. You have successfully bridged the gap from authentication to real data fetching and display. The application now provides genuine value. You are ready to proceed to **Step 4**, where we will tackle the option chain.
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Css
IGNORE_WHEN_COPYING_END